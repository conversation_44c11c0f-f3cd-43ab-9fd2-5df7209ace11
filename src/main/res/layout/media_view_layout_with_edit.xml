<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginBottom="@dimen/meridianSpacing400"
        android:background="?meridianThemeBackgroundPrimaryDefault">

    <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

        <LinearLayout
                android:id="@+id/media_title_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toTopOf="parent"
                android:layout_marginTop="@dimen/meridianSpacing400"
                android:layout_marginHorizontal="@dimen/meridianSpacing400"
                android:orientation="vertical">

            <com.amazon.meridian.text.MeridianText
                    android:id="@+id/media_title"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:meridianTextType="h100" />

            <com.amazon.meridian.text.MeridianText
                    android:id="@+id/media_subtitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:meridianTextType="b300" />
        </LinearLayout>

        <com.amazon.meridian.button.MeridianButton
                android:id="@+id/take_photo_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/meridianSpacing400"
                android:layout_marginTop="@dimen/meridianSpacing400"
                android:layout_marginEnd="@dimen/meridianSpacing300"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/media_title_container"
                app:meridianButtonSize="large"
                app:meridianButtonType="tertiary"
                app:meridianIconIcon="camera"
                app:meridianIconTint="@color/meridianOrange300" />

        <com.amazon.meridian.button.MeridianButton
                android:id="@+id/add_photo_button"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/meridianSpacing300"
                app:layout_constraintStart_toEndOf="@id/take_photo_button"
                app:layout_constraintTop_toTopOf="@id/take_photo_button"
                app:meridianButtonSize="large"
                app:meridianButtonType="tertiary"
                app:meridianIconIcon="camera_roll"
                app:meridianIconTint="@color/meridianOrange300" />

        <com.amazon.meridian.text.MeridianText
                android:id="@+id/preview_media_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/meridianSpacing400"
                android:layout_marginTop="@dimen/meridianSpacing400"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@id/take_photo_button"
                app:meridianTextType="h100" />

        <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/preview_container"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/meridianSpacing400"
                android:layout_marginTop="@dimen/meridianSpacing300"
                android:visibility="gone"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/preview_media_title">

            <ImageView
                    android:id="@+id/preview_media_thumbnail"
                    android:layout_width="128dp"
                    android:layout_height="128dp"
                    android:scaleType="centerCrop"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            <FrameLayout
                    android:id="@+id/delete_button"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="@id/preview_media_thumbnail"
                    app:layout_constraintTop_toTopOf="@id/preview_media_thumbnail">

                <ImageView
                        android:layout_width="32dp"
                        android:layout_height="32dp"
                        android:layout_gravity="center"
                        android:background="@drawable/circle_button_themed"
                        android:paddingHorizontal="8dp"
                        android:paddingBottom="6dp"
                        android:src="@drawable/ic_delete_themed" />

            </FrameLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Edit Button with Icon and Text -->
        <LinearLayout
                android:id="@+id/edit_button_container"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/meridianSpacing450"
                android:layout_marginBottom="@dimen/meridianSpacing300"
                android:background="@drawable/rounded_rect_background"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="8dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent">

            <ImageView
                    android:id="@+id/edit_icon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:contentDescription="Edit"
                    android:src="@drawable/annotation_edit_pencil"
                    app:tint="?android:attr/textColorPrimary" />

            <com.amazon.meridian.text.MeridianText
                    android:id="@+id/edit_text"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="Draw"
                    android:textSize="16sp"
                    app:meridianTextType="b200" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
