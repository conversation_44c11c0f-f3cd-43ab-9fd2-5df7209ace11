package com.amazon.maps.engagement

import android.content.Context
import com.amazon.geo.mapsv2.internal.mapbox.AGMetricsConfig
import com.amazon.geo.mapsv2.location.LocationEngineUtil
import com.amazon.geo.routing.GranTorinoInitializer
import com.amazon.geo.routingv2.elcamino.RouteStore
import com.amazon.location.model.geofence.LatLng
import com.amazon.maps.engagement.model.FeedbackInput
import com.amazon.maps.engagement.model.MapsFeedbackFeatureMetadata
import com.amazon.maps.engagement.model.MapsFeedbackStopInfo
import com.amazon.maps.engagement.model.MapsFeedbackType
import com.amazon.maps.visual.model.MapStylesheet
import com.google.gson.Gson
import com.google.gson.JsonObject

internal class HeadlessStateMachineInputBuilder(private val context: Context) {
    private val configProvider: AGMetricsConfig = AGMetricsConfig
    private val routeStore: RouteStore = GranTorinoInitializer.routeStore

    fun buildInput(
        topic: String,
        category: String,
        feedback: JsonObject,
        mapsFeedbackStopInfo: MapsFeedbackStopInfo? = null,
        mapStylesheetInfo: MapStylesheet? = null,
        suppressConfirmationResponseIdentifier: String? = null,
    ): JsonObject {
        val route = mapsFeedbackStopInfo?.addressId?.let { routeStore.getRouteForAddressId(it) }
        val userLocation = LocationEngineUtil.getLocationPerLocationEngine()
        val feedbackType = extractFeedbackType(feedback)

        val mapsFeedbackMetadata = Gson().toJsonTree(
            MapsFeedbackFeatureMetadata(
                countryCode = configProvider.countryCode(),
                operatingArea = configProvider.operatingArea(),
                obfuscatedTransporterId = configProvider.transporterId(),
                userAgent = context.packageName,
                userPosition = userLocation?.let { LatLng(it.latitude, it.longitude) } ?: DEFAULT_LAT_LNG,
                userType = configProvider.transporterType(),
                feedbackType = feedbackType,
                routeId = route?.routeId,
                subRouteId = route?.subRouteId ?: mapsFeedbackStopInfo?.stopId,
                address = route?.address,
                addressId = mapsFeedbackStopInfo?.addressId,
                requestId = route?.requestId,
                stopId = mapsFeedbackStopInfo?.stopId,
                scannableId = mapsFeedbackStopInfo?.scannableId,
                stylesheetVersion = mapStylesheetInfo?.version,
                stylesheetUrl = mapStylesheetInfo?.url,
                roadEntryGeocode = mapsFeedbackStopInfo?.roadEntryGeocode,
                deliveryPointGeocode = mapsFeedbackStopInfo?.deliveryPointGeocode,
            ),
        ).asJsonObject

        val feedbackInput = FeedbackInput(
            topic = topic,
            category = category,
            featureSpecificData = mergeJson(feedback, mapsFeedbackMetadata).toString(),
            suppressConfirmationResponseIdentifier = suppressConfirmationResponseIdentifier,
        )

        return Gson().toJsonTree(feedbackInput).asJsonObject
    }

    private fun mergeJson(
        target: JsonObject,
        source: JsonObject,
    ): JsonObject {
        source.keySet().forEach { key ->
            target.add(key, source.get(key))
        }
        return target
    }

    private fun extractFeedbackType(feedback: JsonObject): MapsFeedbackType {
        return try {
            MapsFeedbackType.valueOf(feedback.get("feedbackType").toString().trim('"'))
        } catch (e: Exception) {
            MapsFeedbackType.UNKNOWN
        }
    }

    companion object {
        internal val DEFAULT_LAT_LNG = LatLng(0.0, 0.0)
    }
}
