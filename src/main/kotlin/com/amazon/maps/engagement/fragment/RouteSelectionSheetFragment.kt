package com.amazon.maps.engagement.fragment

import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import com.amazon.geo.mapsv2.util.toJsonString
import com.amazon.maps.engagement.model.FeedbackOption
import com.amazon.maps.engagement.model.FeedbackResponse
import com.amazon.maps.engagement.model.Question
import com.amazon.maps.engagement.model.QuestionType
import com.amazon.maps.engagement.model.contextualfeedback.ContextualFeedbackStopInfo
import com.amazon.maps.engagement.model.contextualfeedback.ExpandableMapQuestionResponseMetadata
import com.amazon.maps.engagement.model.contextualfeedback.QuestionIdConstants
import com.amazon.maps.engagement.model.contextualfeedback.SheetIdConstants
import com.amazon.maps.engagement.sdk.R
import com.amazon.maps.engagement.utils.ContextualFeedbackRadioListQuestionsBuilder
import com.amazon.maps.engagement.utils.ContextualFeedbackResponseMetadataBuilder
import com.amazon.maps.engagement.utils.ExpandableMapUtils
import com.amazon.maps.engagement.utils.MediaUtils
import com.amazon.maps.engagement.viewmodel.BaseFeedbackViewModel
import com.amazon.maps.engagement.viewmodel.ContextualFeedbackViewModel
import com.amazon.maps.engagement.widget.BaseWidget
import com.amazon.maps.engagement.widget.DropdownWidget
import com.amazon.maps.engagement.widget.ExpandableMapWidget
import com.amazon.maps.engagement.widget.MediaWidget
import com.amazon.maps.engagement.widget.RadioListWidget
import com.amazon.meridian.divider.MeridianDivider
import com.amazon.rds.buttonlayout.RDSButtonLayout
import com.google.gson.Gson

class RouteSelectionSheetFragment : BaseMediaSheetFragment() {
    private var submitButton: RDSButtonLayout? = null
    private val allRoutes: MutableList<Pair<ContextualFeedbackStopInfo?, ContextualFeedbackStopInfo>> = mutableListOf()
    override fun getViewModelClass(): Class<out BaseFeedbackViewModel> = ContextualFeedbackViewModel::class.java

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        submitButton = view.findViewById<RDSButtonLayout>(R.id.answer_question_submit_button).apply {
            isEnabled = false
            setPrimaryButtonOnClickListener { submitFeedback() }
        }
    }

    override fun setupWidgets(view: View) {
        val container = view.findViewById<ViewGroup>(R.id.widget_container)
        sheet.questions.forEach { question ->
            createWidget(question)?.let { widget ->
                widgets[question.identifier] = widget
                addWidgetToContainer(container, question, widget)
            }
        }
    }

    override fun createWidget(question: Question): BaseWidget? {
        return when (question.type) {
            QuestionType.RADIO_LIST ->
                RadioListWidget(
                    requireContext(),
                    question,
                    this::onWidgetResponseChanged,
                    options = getRadioListOptions(),
                    isCollapsible = true,
                )

            QuestionType.DROPDOWN -> {
                val options = getDropdownListOptions()
                DropdownWidget(
                    requireContext(),
                    question,
                    this::onWidgetResponseChanged,
                    options = options,
                    defaultItemText = if (options.isEmpty()) context?.getString(R.string.feedback_no_route_available) else null,
                )
            }

            QuestionType.EXPANDABLE_MAP ->
                ExpandableMapWidget(
                    requireContext(),
                    question,
                    this::onWidgetResponseChanged,
                    shouldShowCenterPin = true,
                )

            QuestionType.ATTACH_MEDIA -> {
                val contextualViewModel = viewModel as? ContextualFeedbackViewModel
                MediaWidget(
                    requireContext(),
                    question,
                    this::onWidgetResponseChanged,
                    this,
                    scope,
                    contextualViewModel,
                )
            }

            else -> return null
        }
    }

    override fun onWidgetResponseChanged(
        questionId: String,
        response: FeedbackResponse?,
    ) {
        super.onWidgetResponseChanged(questionId, response)

        when (questionId) {
            QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID -> {
                response?.let {
                    responses[questionId] = it
                    if (it.answerIdList.isEmpty()) {
                        handleEmptyMediaResponse()
                    }
                }
            }

            QuestionIdConstants.ROUTE_SELECTION_PROBLEM_SELECTOR_QUESTION_ID -> {
                response?.let {
                    // Special handling of response only on first radio list selection
                    if (isInitialProblemSelection()) {
                        handleRouteSelectionProblemResponseChange(it)
                    } else {
                        // Only update map widget response with new answerIdList in sheet responses list
                        val expandableMapResponse = createRouteSelectionMapResponse(it.answerIdList)
                        responses[expandableMapResponse.questionId] = expandableMapResponse
                    }
                }
            }

            QuestionIdConstants.ROUTE_SELECTOR_QUESTION_ID -> {
                response?.let { handleRouteSelectorResponseChange() }
            }
        }

        submitButton?.isEnabled = isFulfilled()
    }

    private fun submitFeedback() {
        responses[QuestionIdConstants.ROUTE_SELECTION_PROBLEM_SELECTOR_QUESTION_ID]?.let { routeSelectionProblemResponse ->
            val processedDraggableMapResponse = createDraggableMapResponse(routeSelectionProblemResponse)
            responses[processedDraggableMapResponse.questionId] = processedDraggableMapResponse
        }
        processResponses()
    }

    private fun getDropdownListOptions(): List<FeedbackOption> {
        val context = requireContext()
        val feedbackOptions = mutableListOf<FeedbackOption>()

        (viewModel as? ContextualFeedbackViewModel)?.config?.stops?.let { stops ->
            stops.forEachIndexed { index, stop ->
                val stopPair = Pair(stops.getOrNull(index + 1), stop)
                allRoutes.add(stopPair)

                val stopIdPair = StopIdPair(stopPair.first?.stopId, stopPair.second.stopId)
                val streetAddressString = stop.streetAddress.takeUnless { it == DEFAULT_FEEDBACK_STRING }.orEmpty()
                val routeOptionText = stopPair.first?.let { previousStop ->
                    String.format(context.getString(R.string.feedback_route_from_stop_to_stop), previousStop.stopNumber, stop.stopNumber, streetAddressString)
                } ?: String.format(context.getString(R.string.feedback_to_stop), stop.stopNumber, streetAddressString)
                feedbackOptions.add(
                    FeedbackOption(
                        identifier = Gson().toJson(stopIdPair).toString(),
                        text = routeOptionText,
                        subtext = ExpandableMapUtils.generateAddressText(context, stop),
                    ),
                )
            }
        }
        return feedbackOptions
    }

    private fun getRadioListOptions(): List<FeedbackOption> {
        val feedbackOptions = mutableListOf<FeedbackOption>()

        feedbackOptions.addAll(
            getListOfAnswerChoicesForIds(
                ContextualFeedbackRadioListQuestionsBuilder.MAP_FEATURE_TYPE_ROAD_AND_INTERSECTION_QUESTION_ID_LIST,
            ),
        )

        return feedbackOptions
    }

    private fun getListOfAnswerChoicesForIds(ids: List<Int>): List<FeedbackOption> {
        val context = requireContext()
        return ids.map { id ->
            ContextualFeedbackRadioListQuestionsBuilder(context).questionsForFeatureTypeMap.values.toList().first { context.getString(id) == it.text }
        }
    }

    private fun addWidgetToContainer(
        container: ViewGroup,
        question: Question,
        widget: View,
    ) {
        if (question.identifier == QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID) {
            setupMediaWidget(container, widget)
            return
        }

        container.addView(widget)

        // Only show the radio list on initial sheet load
        if (!isProblemSelectorQuestion(question.identifier)) {
            widget.visibility = View.INVISIBLE
        }

        // Add divider under radio list
        if (isProblemSelectorQuestion(question.identifier)) {
            MeridianDivider(requireContext()).apply {
                setSize(MeridianDivider.Size.SMALL)
                container.addView(this)
            }
        }
    }

    private fun setupMediaWidget(
        container: ViewGroup,
        widget: View,
    ) {
        if (wasPhotoAddedFirst()) {
            container.addView(widget, 0)
            widget.visibility = View.VISIBLE
            getAttachMediaResponseFromCategorySelectionSheet()?.let { mediaResponse ->
                setResponseForWidget(QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID, mediaResponse)
            }
        } else {
            container.addView(widget)
            widget.visibility = View.INVISIBLE
        }
    }

    private fun handleRouteSelectionProblemResponseChange(response: FeedbackResponse) {
        val stops: List<ContextualFeedbackStopInfo> = (viewModel as? ContextualFeedbackViewModel)?.config?.stops
            ?: emptyList()

        widgets[QuestionIdConstants.ROUTE_SELECTION_EXPANDABLE_MAP_QUESTION_ID]?.apply {
            visibility = View.VISIBLE
            (this as? ExpandableMapWidget)?.drawAllStopPins(stops)
        }
        widgets[QuestionIdConstants.ROUTE_SELECTOR_QUESTION_ID]?.apply { visibility = View.VISIBLE }

        ExpandableMapUtils.getLatestRoute(stops)?.let { routeStops ->
            setResponseForWidget(QuestionIdConstants.ROUTE_SELECTOR_QUESTION_ID, createRouteSelectorResponse(routeStops))

            val expandableMapResponse = createRouteSelectionMapResponse(
                answerIdList = response.answerIdList,
                routeStops = routeStops,
            )
            setResponseForWidget(QuestionIdConstants.ROUTE_SELECTION_EXPANDABLE_MAP_QUESTION_ID, expandableMapResponse)
        } ?: run {
            // If latest route was null due to empty stop list
            val expandableMapResponse = createRouteSelectionMapResponse(response.answerIdList)
            setResponseForWidget(QuestionIdConstants.ROUTE_SELECTION_EXPANDABLE_MAP_QUESTION_ID, expandableMapResponse)
        }

        widgets[QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID]?.apply { visibility = View.VISIBLE }
    }

    private fun handleRouteSelectorResponseChange() {
        val problemSelectorResponse = responses[QuestionIdConstants.ROUTE_SELECTION_PROBLEM_SELECTOR_QUESTION_ID]

        getSelectedRoute()?.let { selectedRoute ->
            val expandableMapResponse = createRouteSelectionMapResponse(
                answerIdList = problemSelectorResponse?.answerIdList ?: emptyList(),
                routeStops = listOfNotNull(selectedRoute.first, selectedRoute.second),
            )
            setResponseForWidget(QuestionIdConstants.ROUTE_SELECTION_EXPANDABLE_MAP_QUESTION_ID, expandableMapResponse)
        }
    }

    private fun handleEmptyMediaResponse() {
        (viewModel as? ContextualFeedbackViewModel)?.apply {
            clearMediaResponses()
            onMediaDeleted()
        }
    }

    private fun wasPhotoAddedFirst(): Boolean {
        val categorySelectionSheetResponse =
            viewModel.getResponsesForSheet(SheetIdConstants.CATEGORY_SELECTION_SHEET_ID)
        return categorySelectionSheetResponse
            .find { it.questionId == QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID }
            ?.answerIdList
            ?.isNotEmpty() == true
    }

    private fun getAttachMediaResponseFromCategorySelectionSheet(): FeedbackResponse? {
        return viewModel.getResponsesForSheet(SheetIdConstants.CATEGORY_SELECTION_SHEET_ID)
            .find { it.questionId == QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID }
    }

    private fun isInitialProblemSelection(): Boolean {
        return responses[QuestionIdConstants.ROUTE_SELECTION_EXPANDABLE_MAP_QUESTION_ID]?.answerIdList?.isEmpty() ?: true
    }

    private fun isProblemSelectorQuestion(questionId: String): Boolean {
        return questionId == QuestionIdConstants.ROUTE_SELECTION_PROBLEM_SELECTOR_QUESTION_ID
    }

    private fun getSelectedRoute(): Pair<ContextualFeedbackStopInfo?, ContextualFeedbackStopInfo>? {
        val routeSelectorResponse = widgets[QuestionIdConstants.ROUTE_SELECTOR_QUESTION_ID]?.getResponse()
        return allRoutes.find {
            Gson().toJson(StopIdPair(it.first?.stopId, it.second.stopId)).toString() == routeSelectorResponse?.answerIdList?.first()
        }
    }

    // region Feedback response helper functions
    private fun setResponseForWidget(
        questionId: String,
        feedbackResponse: FeedbackResponse,
    ) {
        widgets[questionId]?.setResponse(feedbackResponse)
        responses[questionId] = feedbackResponse
    }

    private fun createRouteSelectorResponse(routeStartEndStops: List<ContextualFeedbackStopInfo>): FeedbackResponse {
        val stopIdPair = StopIdPair(
            routeStartEndStops.getOrNull(1)?.stopId,
            routeStartEndStops[0].stopId,
        )
        return FeedbackResponse(
            questionId = QuestionIdConstants.ROUTE_SELECTOR_QUESTION_ID,
            answerIdList = listOf(Gson().toJson(stopIdPair).toString()),
        )
    }

    private fun createRouteSelectionMapResponse(
        answerIdList: List<String>,
        routeStops: List<ContextualFeedbackStopInfo>? = null,
    ): FeedbackResponse {
        return FeedbackResponse(
            questionId = QuestionIdConstants.ROUTE_SELECTION_EXPANDABLE_MAP_QUESTION_ID,
            answerIdList = answerIdList,
            metadata = routeStops?.let {
                ExpandableMapQuestionResponseMetadata(
                    stopsListForViewport = routeStops,
                ).toJsonString()
            } ?: responses[QuestionIdConstants.ROUTE_SELECTION_EXPANDABLE_MAP_QUESTION_ID]?.metadata,

        )
    }

    private fun createDraggableMapResponse(routeSelectionProblemResponse: FeedbackResponse): FeedbackResponse {
        val expandableMapResponse = widgets[QuestionIdConstants.ROUTE_SELECTION_EXPANDABLE_MAP_QUESTION_ID]?.getResponse()
        val mediaResponse = widgets[QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID]?.getResponse()
        return FeedbackResponse(
            questionId = QuestionIdConstants.DRAGGABLE_MAP_QUESTION_ID,
            answerIdList = routeSelectionProblemResponse.answerIdList,
            metadata = ContextualFeedbackResponseMetadataBuilder.generateMetadata(
                context = requireContext(),
                feedbackType = routeSelectionProblemResponse.answerIdList.first(),
                location = ExpandableMapUtils.extractFeedbackPositionFromExpandableMapMetadata(
                    expandableMapResponse?.metadata,
                ),
                stop = getSelectedRoute()?.second,
                mediaMetadata = MediaUtils.getMediaMetadata(mediaResponse),
                mapsFeedbackMetadata = (viewModel as? ContextualFeedbackViewModel)?.config?.feedbackMetadata,
            ).toJsonString(),
        )
    }
    // endregion

    companion object {
        private const val DEFAULT_FEEDBACK_STRING = "Attribute is not populated"
    }
}

internal data class StopIdPair(
    val startStopId: String?,
    val endStopId: String,
)
