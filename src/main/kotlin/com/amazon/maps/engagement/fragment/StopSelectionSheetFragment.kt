package com.amazon.maps.engagement.fragment

import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import com.amazon.geo.mapsv2.location.LocationEngineUtil
import com.amazon.geo.mapsv2.util.toJsonString
import com.amazon.location.model.geofence.LatLng
import com.amazon.majixplatform.log.logError
import com.amazon.maps.engagement.model.FeedbackOption
import com.amazon.maps.engagement.model.FeedbackResponse
import com.amazon.maps.engagement.model.MediaMetadata
import com.amazon.maps.engagement.model.Question
import com.amazon.maps.engagement.model.QuestionType
import com.amazon.maps.engagement.model.contextualfeedback.ContextualFeedbackStopInfo
import com.amazon.maps.engagement.model.contextualfeedback.ExpandableMapQuestionResponseMetadata
import com.amazon.maps.engagement.model.contextualfeedback.QuestionIdConstants
import com.amazon.maps.engagement.model.contextualfeedback.SheetIdConstants
import com.amazon.maps.engagement.sdk.R
import com.amazon.maps.engagement.utils.ContextualFeedbackRadioListQuestionsBuilder
import com.amazon.maps.engagement.utils.ContextualFeedbackResponseMetadataBuilder
import com.amazon.maps.engagement.utils.ExpandableMapUtils
import com.amazon.maps.engagement.utils.ExpandableMapUtils.generateStopText
import com.amazon.maps.engagement.utils.MapsEngagementConfigurations
import com.amazon.maps.engagement.utils.MediaUtils
import com.amazon.maps.engagement.viewmodel.BaseFeedbackViewModel
import com.amazon.maps.engagement.viewmodel.ContextualFeedbackViewModel
import com.amazon.maps.engagement.widget.BaseWidget
import com.amazon.maps.engagement.widget.DropdownWidget
import com.amazon.maps.engagement.widget.ExpandableMapWidget
import com.amazon.maps.engagement.widget.MediaWidget
import com.amazon.maps.engagement.widget.RadioListWidget
import com.amazon.meridian.divider.MeridianDivider
import com.amazon.rds.buttonlayout.RDSButtonLayout
import com.google.gson.Gson

class StopSelectionSheetFragment : BaseMediaSheetFragment() {
    private var submitButton: RDSButtonLayout? = null
    override fun getViewModelClass(): Class<out BaseFeedbackViewModel> = ContextualFeedbackViewModel::class.java

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        submitButton = view.findViewById<RDSButtonLayout>(R.id.answer_question_submit_button).apply {
            isEnabled = false
            setPrimaryButtonOnClickListener { submitFeedback() }
        }
    }

    override fun setupWidgets(view: View) {
        val container = view.findViewById<ViewGroup>(R.id.widget_container)
        sheet.questions.forEach { question ->
            createWidget(question)?.let { widget ->
                widgets[question.identifier] = widget
                when {
                    // Add photo widget and show on the top if photo added from category selection sheet
                    question.identifier == QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID -> {
                        if (wasPhotoAddedFirst()) {
                            container.addView(widget, 0)
                            widget.visibility = View.VISIBLE

                            viewModel.getResponsesForSheet(SheetIdConstants.CATEGORY_SELECTION_SHEET_ID)
                                .find { it.questionId == QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID }
                                ?.let { mediaResponse ->
                                    widget.setResponse(mediaResponse)
                                    responses[QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID] =
                                        mediaResponse
                                }
                        } else {
                            container.addView(widget)
                            widget.visibility = View.INVISIBLE
                        }
                    }

                    else -> {
                        container.addView(widget)
                        // Only show the radio list on initial sheet load
                        if (!isProblemSelectorQuestion(question.identifier)) {
                            widget.visibility = View.INVISIBLE
                        }
                        // Add divider under radio list
                        if (isProblemSelectorQuestion(question.identifier)) {
                            MeridianDivider(requireContext()).apply {
                                setSize(MeridianDivider.Size.SMALL)
                                container.addView(this)
                            }
                        }
                    }
                }
            }
        }
    }

    override fun createWidget(question: Question): BaseWidget? {
        return when (question.type) {
            QuestionType.RADIO_LIST ->
                RadioListWidget(
                    requireContext(),
                    question,
                    this::onWidgetResponseChanged,
                    options = getRadioListOptions(),
                    isCollapsible = true,
                )

            QuestionType.DROPDOWN -> {
                val options = getDropdownListOptions()
                DropdownWidget(
                    requireContext(),
                    question,
                    this::onWidgetResponseChanged,
                    options = options,
                    defaultItemText = if (options.isEmpty()) context?.getString(R.string.feedback_no_stop_available) else null,
                )
            }

            QuestionType.EXPANDABLE_MAP ->
                ExpandableMapWidget(
                    requireContext(),
                    question,
                    this::onWidgetResponseChanged,
                    shouldShowCenterPin = false,
                )

            QuestionType.ATTACH_MEDIA -> {
                val contextualViewModel = viewModel as? ContextualFeedbackViewModel
                MediaWidget(
                    requireContext(),
                    question,
                    this::onWidgetResponseChanged,
                    this,
                    scope,
                    contextualViewModel,
                )
            }

            else -> return null
        }
    }

    private fun getDropdownListOptions(): List<FeedbackOption> {
        return (viewModel as? ContextualFeedbackViewModel)?.config?.stops?.map { stop ->
            FeedbackOption(
                identifier = stop.stopId,
                text = generateStopText(requireContext(), stop),
            )
        } ?: emptyList()
    }

    private fun getRadioListOptions(): List<FeedbackOption> {
        val feedbackOptions = mutableListOf<FeedbackOption>()

        if (MapsEngagementConfigurations.isShowWrongStopLocationOnlyEnabled()) {
            return getListOfAnswerChoicesForIds(
                listOf(
                    R.string.feedback_delivery_pin_is_in_the_wrong_place,
                    R.string.feedback_navigation_took_me_to_the_wrong_destination,
                ),
            )
        }

        feedbackOptions.addAll(
            getListOfAnswerChoicesForIds(
                ContextualFeedbackRadioListQuestionsBuilder.MAP_FEATURE_TYPE_BUILDING_QUESTION_ID_LIST,
            ),
        )

        return feedbackOptions
    }

    override fun onWidgetResponseChanged(
        questionId: String,
        response: FeedbackResponse?,
    ) {
        super.onWidgetResponseChanged(questionId, response)

        when (questionId) {
            QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID -> {
                response?.let {
                    responses[questionId] = it
                    if (it.answerIdList.isEmpty()) {
                        (viewModel as? ContextualFeedbackViewModel)?.apply {
                            clearMediaResponses()
                            onMediaDeleted()
                        }
                    }
                }
            }

            QuestionIdConstants.STOP_SELECTION_PROBLEM_SELECTOR_QUESTION_ID -> {
                response?.let {
                    // Special handling of response only on first radio list selection
                    if (isInitialProblemSelection()) {
                        handleStopSelectionProblemResponseChange(it)
                    } else {
                        // Only update map widget response with new answerIdList in sheet responses list
                        val expandableMapResponse = FeedbackResponse(
                            questionId = QuestionIdConstants.ROUTE_SELECTION_EXPANDABLE_MAP_QUESTION_ID,
                            answerIdList = it.answerIdList,
                            metadata = responses[QuestionIdConstants.ROUTE_SELECTION_EXPANDABLE_MAP_QUESTION_ID]?.metadata,
                        )
                        responses[expandableMapResponse.questionId] = expandableMapResponse
                    }
                }
            }

            QuestionIdConstants.STOP_SELECTOR_QUESTION_ID -> {
                response?.let { handleStopSelectorResponseChange(it) }
            }
        }

        submitButton?.isEnabled = isFulfilled()
    }

    private fun submitFeedback() {
        val stops: List<ContextualFeedbackStopInfo> =
            (viewModel as? ContextualFeedbackViewModel)?.config?.stops ?: emptyList()

        val expandableMapResponse = widgets[QuestionIdConstants.STOP_SELECTION_EXPANDABLE_MAP_QUESTION_ID]?.getResponse()
        val stopSelectorResponse = widgets[QuestionIdConstants.STOP_SELECTOR_QUESTION_ID]?.getResponse()
        val mediaResponse = widgets[QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID]?.getResponse()

        responses[QuestionIdConstants.STOP_SELECTION_PROBLEM_SELECTOR_QUESTION_ID]?.let { stopSelectionProblemResponse ->
            val processedDraggableMapResponse = FeedbackResponse(
                questionId = QuestionIdConstants.DRAGGABLE_MAP_QUESTION_ID,
                answerIdList = stopSelectionProblemResponse.answerIdList,
                metadata = ContextualFeedbackResponseMetadataBuilder.generateMetadata(
                    context = requireContext(),
                    feedbackType = stopSelectionProblemResponse.answerIdList.first(),
                    location = ExpandableMapUtils.extractFeedbackPositionFromExpandableMapMetadata(
                        expandableMapResponse?.metadata,
                    ),
                    stop = stops.firstOrNull { it.stopId == stopSelectorResponse?.answerIdList?.first() },
                    mediaMetadata = MediaUtils.getMediaMetadata(mediaResponse),
                    mapsFeedbackMetadata = (viewModel as? ContextualFeedbackViewModel)?.config?.feedbackMetadata,
                ).toJsonString(),
            )
            responses[processedDraggableMapResponse.questionId] = processedDraggableMapResponse
        }
        processResponses()
    }

    private fun handleStopSelectionProblemResponseChange(response: FeedbackResponse) {
        val stops = (viewModel as? ContextualFeedbackViewModel)?.config?.stops.orEmpty()

        val mapWidget = widgets[QuestionIdConstants.STOP_SELECTION_EXPANDABLE_MAP_QUESTION_ID]?.apply { visibility = View.VISIBLE }
        (mapWidget as? ExpandableMapWidget)?.let {
            it.drawAllStopPins(stops)
        }

        val dropdownWidget = widgets[QuestionIdConstants.STOP_SELECTOR_QUESTION_ID]?.apply { visibility = View.VISIBLE }

        val userLocation = LocationEngineUtil.getLocationPerLocationEngine()?.let { LatLng(it.latitude, it.longitude) }
        val nearestStop = when {
            wasPhotoAddedFirst() -> getMediaLatLngFromAttachMediaResponse()?.let { mediaLatLng ->
                ExpandableMapUtils.getNearestStopsToAttachedMedia(mediaLatLng, stops, 1).firstOrNull()
            }

            else -> ExpandableMapUtils.getNearestStop(userLocation, stops)
        }

        nearestStop?.let {
            dropdownWidget?.let { dropdownWidget ->
                val stopSelectorResponse = FeedbackResponse(
                    questionId = QuestionIdConstants.STOP_SELECTOR_QUESTION_ID,
                    answerIdList = listOf(it.stopId),
                )
                dropdownWidget.setResponse(stopSelectorResponse)
                responses[stopSelectorResponse.questionId] = stopSelectorResponse
            }
            mapWidget?.let { mapWidget ->
                val expandableMapResponse = FeedbackResponse(
                    questionId = QuestionIdConstants.STOP_SELECTION_EXPANDABLE_MAP_QUESTION_ID,
                    answerIdList = responses[QuestionIdConstants.STOP_SELECTION_PROBLEM_SELECTOR_QUESTION_ID]?.answerIdList ?: response.answerIdList,
                    metadata = ExpandableMapQuestionResponseMetadata(
                        stopsListForViewport = listOf(it),
                    ).toJsonString(),
                )
                mapWidget.setResponse(expandableMapResponse)
                responses[expandableMapResponse.questionId] = expandableMapResponse
            }
        } ?: run {
            // If nearest stop was null due to empty stop list
            mapWidget?.let { mapWidget ->
                val expandableMapResponse = FeedbackResponse(
                    questionId = QuestionIdConstants.STOP_SELECTION_EXPANDABLE_MAP_QUESTION_ID,
                    answerIdList = responses[QuestionIdConstants.STOP_SELECTION_PROBLEM_SELECTOR_QUESTION_ID]?.answerIdList ?: response.answerIdList,
                )
                mapWidget.setResponse(expandableMapResponse)
                responses[expandableMapResponse.questionId] = expandableMapResponse
            }
        }

        widgets[QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID]?.apply { visibility = View.VISIBLE }
    }

    private fun handleStopSelectorResponseChange(response: FeedbackResponse) {
        val mapWidget = widgets[QuestionIdConstants.STOP_SELECTION_EXPANDABLE_MAP_QUESTION_ID]
        val stops: List<ContextualFeedbackStopInfo> =
            (viewModel as? ContextualFeedbackViewModel)?.let { viewModel ->
                viewModel.config.stops
            } ?: emptyList()

        val selectedStopIndex = stops.indexOfFirst { it.stopId == response.answerIdList.first() }
        if (selectedStopIndex >= 0) {
            val selectedStop = stops[selectedStopIndex]
            mapWidget?.let {
                val expandableMapResponse = FeedbackResponse(
                    questionId = QuestionIdConstants.STOP_SELECTION_EXPANDABLE_MAP_QUESTION_ID,
                    answerIdList = responses[QuestionIdConstants.STOP_SELECTION_PROBLEM_SELECTOR_QUESTION_ID]?.answerIdList ?: emptyList(),
                    metadata = ExpandableMapQuestionResponseMetadata(
                        stopsListForViewport = listOf(selectedStop),
                    ).toJsonString(),
                )
                it.setResponse(expandableMapResponse)
                responses[expandableMapResponse.questionId] = expandableMapResponse
            }
        }
    }

    private fun getListOfAnswerChoicesForIds(ids: List<Int>): List<FeedbackOption> {
        val context = requireContext()
        return ids.map { id ->
            ContextualFeedbackRadioListQuestionsBuilder(context).questionsForFeatureTypeMap.values.toList().first {
                context.getString(id) == it.text
            }
        }
    }

    private fun wasPhotoAddedFirst(): Boolean {
        val categorySelectionSheetResponse =
            viewModel.getResponsesForSheet(SheetIdConstants.CATEGORY_SELECTION_SHEET_ID)
        return categorySelectionSheetResponse
            .find { it.questionId == QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID }
            ?.answerIdList
            ?.isNotEmpty() == true
    }

    private fun getMediaLatLngFromAttachMediaResponse(): LatLng? {
        val mediaWidget = widgets[QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID]
        return try {
            Gson().fromJson(mediaWidget?.getResponse()?.metadata, MediaMetadata::class.java)
                ?.let { LatLng(it.latitude, it.longitude) }
        } catch (e: Exception) {
            logError(throwable = e) { "Unable to parse into AttachMediaMetadata: ${mediaWidget?.getResponse()?.metadata}" }
            null
        }
    }

    private fun isInitialProblemSelection(): Boolean {
        return responses[QuestionIdConstants.STOP_SELECTION_EXPANDABLE_MAP_QUESTION_ID]?.answerIdList?.isEmpty() ?: true
    }

    private fun isProblemSelectorQuestion(questionId: String): Boolean {
        return questionId == QuestionIdConstants.STOP_SELECTION_PROBLEM_SELECTOR_QUESTION_ID
    }
}
