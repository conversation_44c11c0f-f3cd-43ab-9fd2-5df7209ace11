package com.amazon.maps.engagement.fragment

import android.os.Bundle
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import com.amazon.majixplatform.featureflag.FeatureFlag
import com.amazon.majixplatform.featureflag.FeatureTreatment
import com.amazon.majixplatform.util.MajixPlatformModule
import com.amazon.maps.engagement.model.FeedbackOption
import com.amazon.maps.engagement.model.FeedbackResponse
import com.amazon.maps.engagement.model.Question
import com.amazon.maps.engagement.model.QuestionType
import com.amazon.maps.engagement.model.contextualfeedback.QuestionIdConstants
import com.amazon.maps.engagement.model.contextualfeedback.ResponseOptionConstants
import com.amazon.maps.engagement.sdk.R
import com.amazon.maps.engagement.utils.MapsEngagementConfigurations
import com.amazon.maps.engagement.viewmodel.BaseFeedbackViewModel
import com.amazon.maps.engagement.viewmodel.ContextualFeedbackViewModel
import com.amazon.maps.engagement.widget.BaseWidget
import com.amazon.maps.engagement.widget.MediaWidget
import com.amazon.maps.engagement.widget.ParentListWidget
import com.amazon.meridian.icon.MeridianIcon
import com.amazon.rds.footer.RDSFooter

class CategorySelectionSheetFragment : BaseMediaSheetFragment() {
    // Feature Flags
    private val featureFlag = MajixPlatformModule.featureFlagging
    private val disableCurrentLocation =
        featureFlag.isFeatureEnabled(FeatureFlag.ContextualFeedbackCurrentLocationDisabled) != FeatureTreatment.C

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        removeSubmitButton(view)
    }

    override fun getViewModelClass(): Class<out BaseFeedbackViewModel> = ContextualFeedbackViewModel::class.java

    override fun onWidgetResponseChanged(
        questionId: String,
        response: FeedbackResponse?,
    ) {
        super.onWidgetResponseChanged(questionId, response)

        when {
            questionId == QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID &&
                response?.answerIdList?.isEmpty() == true -> {
                (viewModel as? ContextualFeedbackViewModel)?.apply {
                    clearMediaResponses()
                    onMediaDeleted()
                }
            }
            questionId != QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID &&
                isFulfilled() -> processResponses()
        }
    }

    override fun createWidget(question: Question): BaseWidget? {
        return when (question.type) {
            QuestionType.PARENT_LIST ->
                ParentListWidget(
                    requireContext(),
                    question,
                    true,
                    this::onWidgetResponseChanged,
                    getProblemSelectorOptions(),
                )

            QuestionType.ATTACH_MEDIA -> {
                val contextualViewModel = viewModel as? ContextualFeedbackViewModel
                MediaWidget(
                    requireContext(),
                    question,
                    this::onWidgetResponseChanged,
                    this,
                    scope,
                    contextualViewModel,
                )
            }
            else -> return null
        }
    }

    private fun getProblemSelectorOptions(): List<FeedbackOption> {
        val context = requireContext()
        val feedbackOptions = mutableListOf<FeedbackOption>()
        if (MapsEngagementConfigurations.isShowWrongStopLocationOnlyEnabled()) {
            feedbackOptions.add(
                FeedbackOption(
                    identifier = ResponseOptionConstants.AT_A_STOP_RESPONSE_ID,
                    text = context.getString(R.string.feedback_building_stop_location_wrong),
                    iconName = MeridianIcon.Icon.GEO_PIN.name,
                ),
            )
        } else {
            feedbackOptions.add(
                FeedbackOption(
                    identifier = ResponseOptionConstants.AT_A_STOP_RESPONSE_ID,
                    text = context.getString(R.string.feedback_at_a_stop),
                    iconName = MeridianIcon.Icon.BUILDING.name,
                ),
            )
            feedbackOptions.add(
                FeedbackOption(
                    identifier = ResponseOptionConstants.ON_THE_WAY_TO_A_STOP_RESPONSE_ID,
                    text = context.getString(R.string.feedback_on_the_road),
                    iconName = MeridianIcon.Icon.DIRECTIONS.name,
                ),
            )

            if (!disableCurrentLocation) {
                FeedbackOption(
                    identifier = ResponseOptionConstants.YOUR_CURRENT_LOCATION_RESPONSE_ID,
                    text = context.getString(R.string.feedback_your_current_location),
                    iconName = MeridianIcon.Icon.LOCATION.name,
                )
            }
        }
        return feedbackOptions
    }

    private fun removeSubmitButton(view: View) {
        val buttonFooter = view.findViewById<RDSFooter>(R.id.buttonFooter)
        val bottomAnchor = view.findViewById<View>(R.id.widget_bottom_anchor)
        buttonFooter.visibility = View.GONE
        (bottomAnchor.layoutParams as ConstraintLayout.LayoutParams).apply {
            topToBottom = ConstraintLayout.LayoutParams.PARENT_ID
            bottomToBottom = ConstraintLayout.LayoutParams.PARENT_ID
        }
    }
}
