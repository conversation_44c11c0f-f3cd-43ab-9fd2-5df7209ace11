package com.amazon.maps.engagement.viewmodel

import android.app.Application
import android.location.Location
import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.amazon.geo.mapsv2.external.IMapsMessageBusAdapter
import com.amazon.geo.mapsv2.util.AmazonMapsModule
import com.amazon.majixplatform.log.logError
import com.amazon.maps.engagement.MediaUploader
import com.amazon.maps.engagement.cache.AssetCache
import com.amazon.maps.engagement.cache.AssetCacheImpl
import com.amazon.maps.engagement.cache.AssetCacheType
import com.amazon.maps.engagement.model.FeedbackResponse
import com.amazon.maps.engagement.model.NavigationStrategyFactory
import com.amazon.maps.engagement.model.Sheet
import com.amazon.maps.engagement.model.contextualfeedback.ContextualFeedbackConfig
import com.amazon.maps.engagement.model.contextualfeedback.ContextualFeedbackInput
import com.amazon.maps.engagement.model.contextualfeedback.ContextualFeedbackResponseMetadata
import com.amazon.maps.engagement.model.contextualfeedback.QuestionIdConstants
import com.amazon.maps.engagement.model.contextualfeedback.SheetIdConstants
import com.amazon.maps.engagement.navigation.contextualfeedback.CategorySelectionSheetNavigationStrategy
import com.amazon.maps.engagement.navigation.contextualfeedback.RouteSelectionSheetNavigationStrategy
import com.amazon.maps.engagement.navigation.contextualfeedback.StopSelectionSheetNavigationStrategy
import com.amazon.maps.engagement.security.AndroidKeyStoreKeyProvider
import com.amazon.maps.engagement.security.ImageEncryptionManager
import com.amazon.maps.engagement.utils.MediaUtils
import com.amazon.maps.engagement.utils.SheetProviderFactory
import com.google.gson.Gson
import com.google.gson.JsonObject
import com.google.gson.JsonParseException

class ContextualFeedbackViewModel(
    private val application: Application,
    internal val config: ContextualFeedbackConfig,
    messageBusProvider: IMapsMessageBusAdapter?,
) : BaseFeedbackViewModel() {

    override val source: String
        get() = SOURCE

    init {
        registerNavigationStrategies()
    }

    private val encryptionKeyAPI = AndroidKeyStoreKeyProvider(application)
    private val imageEncryptionManager = ImageEncryptionManager(encryptionKeyAPI)
    private val assetCache = AssetCacheImpl(
        application,
        imageEncryptionManager,
        AssetCacheType.MAP_MEDIA,
    )

    internal fun getAssetCache(): AssetCache {
        if (!assetCache.isInitialized()) {
            assetCache.initialize()
        }
        return assetCache
    }

    internal fun getImageEncryptionManager(): ImageEncryptionManager = imageEncryptionManager

    private val mediaUploader = MediaUploader(
        messageBusProvider ?: throw IllegalStateException("MessageBusProvider is null"),
    )

    override fun navigateToInitialSheet() {
        createNextSheet(SheetIdConstants.CATEGORY_SELECTION_SHEET_ID)?.let {
            navigateToSheet(it)
        }
    }

    override fun createNextSheet(sheetId: String): Sheet? {
        return SheetProviderFactory(application.applicationContext).createSheet(sheetId)
    }

    override fun getFeedbackJson(): JsonObject {
        val feedback = ContextualFeedbackInput(
            topic = TOPIC,
            category = CATEGORY,
            collectedFeedback = getAllResponses()
                .filter { it.questionId in FEEDBACK_JSON_QUESTIONS }
                .map { it.removeMediaUri() }
                .distinctBy { it.questionId },
            mediaReferenceId = getMediaReferenceId(),
        )
        return Gson().toJsonTree(feedback).asJsonObject
    }

    private fun FeedbackResponse.removeMediaUri(): FeedbackResponse {
        return when (questionId) {
            QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID -> {
                MediaUtils.getMediaMetadata(this).firstOrNull()?.let { metadata ->
                    copy(answerIdList = listOf(metadata.mediaId))
                } ?: this
            }
            else -> this
        }
    }

    override fun onSubmitFeedback() {
        val feedbackResponse = getAllResponses().find {
            it.questionId == QuestionIdConstants.DRAGGABLE_MAP_QUESTION_ID
        }

        val metadata = feedbackResponse?.let {
            try {
                Gson().fromJson(it.metadata, ContextualFeedbackResponseMetadata::class.java)
            } catch (e: JsonParseException) {
                logError { "Cannot retrieve metadata from feedback: ${it.metadata}" }
                null
            }
        }

        metadata?.let {
            metricRecorder.userSubmittedFeedback(
                feedbackId = it.internalFeedbackId,
                feedbackType = it.feedbackType.toString(),
                location = Location("").apply {
                    latitude = it.feedbackPosition.latitude
                    longitude = it.feedbackPosition.longitude
                },
                durationMs = (System.currentTimeMillis() - startTime).toInt(),
                mediaAttached = it.mediaInfo.size,
                addressId = it.addressId,
                requestId = it.requestId,
                routeId = it.routeId,
                subRouteId = it.subRouteId,
                source = source,
            )
        }
    }

    override fun onCancelFeedback() {
        metricRecorder.userCancelledFeedback(
            durationMs = (System.currentTimeMillis() - startTime).toInt(),
            lastSheetId = sheets.last().identifier,
            source = source,
        )
    }

    fun onMediaDeleted() {
        metricRecorder.userDeletedFeedbackMedia(source)
    }

    // TODO
    fun getResponseOptions(questionId: String): List<String> {
        // Options should come from a provider class
        return listOf()
    }

    private fun registerNavigationStrategies() {
        NavigationStrategyFactory.apply {
            register(
                SheetIdConstants.CATEGORY_SELECTION_SHEET_ID,
                lazy {
                    { _: Sheet -> CategorySelectionSheetNavigationStrategy() }
                },
            )

            register(
                SheetIdConstants.STOP_SELECTION_SHEET_ID,
                lazy {
                    { _: Sheet -> StopSelectionSheetNavigationStrategy() }
                },
            )

            register(
                SheetIdConstants.ROUTE_SELECTION_SHEET_ID,
                lazy {
                    { _: Sheet -> RouteSelectionSheetNavigationStrategy() }
                },
            )
        }
    }

    suspend fun prepareAndUploadMedia() {
        val mediaResponse = getAllResponses()
            .find { it.questionId == QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID }
            ?: return

        MediaUtils.getMediaMetadata(mediaResponse).firstOrNull()?.let { metadata ->
            val uriString = mediaResponse.answerIdList.firstOrNull()
            val uri = uriString?.let { Uri.parse(it) }

            uri?.let { uri ->
                MediaUtils.processImageForUpload(application, uri, imageEncryptionManager)?.let { imageBytes ->
                    mediaUploader.uploadMedia(imageBytes, metadata)
                }
            }
        }
    }

    override fun processSheetResponses(
        sheetId: String,
        responses: List<FeedbackResponse>,
    ) {
        if (wasMediaDeleted(responses)) {
            clearMediaResponses()
        }
        super.processSheetResponses(sheetId, responses)
    }

    private fun wasMediaDeleted(responses: List<FeedbackResponse>): Boolean {
        return responses.any { response ->
            response.questionId == QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID &&
                response.answerIdList.isEmpty()
        }
    }

    internal fun clearMediaResponses() {
        responsesBySheet.replaceAll { _, sheetResponses ->
            sheetResponses.filterNot {
                it.questionId == QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID
            }
        }
    }

    private fun getMediaReferenceId(): String {
        val mediaResponse = getAllResponses().find { it.questionId == QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID }
        val mediaMetadata = mediaResponse?.let {
            MediaUtils.getMediaMetadata(mediaResponse)
        }?.firstOrNull()
        return mediaMetadata?.mediaReferenceId ?: ""
    }

    companion object {
        private const val TOPIC = "MapsFeedbackNoFreeText"
        private const val CATEGORY = "MAPS"
        private const val SOURCE = "ContextualFeedback"
        private val FEEDBACK_JSON_QUESTIONS = setOf(
            QuestionIdConstants.PROBLEM_SELECTOR_QUESTION_ID,
            QuestionIdConstants.STOP_SELECTOR_QUESTION_ID,
            QuestionIdConstants.ROUTE_SELECTOR_QUESTION_ID,
            QuestionIdConstants.DRAGGABLE_MAP_QUESTION_ID,
            QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID,
        )
    }
}

class ContextualFeedbackViewModelFactory(
    private val application: Application,
    private val config: ContextualFeedbackConfig,
) : ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T {
        if (modelClass.isAssignableFrom(ContextualFeedbackViewModel::class.java)) {
            @Suppress("UNCHECKED_CAST")
            return ContextualFeedbackViewModel(application, config, AmazonMapsModule.mapsMessageBusAdapter) as T
        }
        throw IllegalArgumentException("Unknown ViewModel class")
    }
}
