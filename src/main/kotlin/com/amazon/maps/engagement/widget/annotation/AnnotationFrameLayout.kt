package com.amazon.maps.engagement.widget.annotation

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import androidx.core.graphics.createBitmap
import com.amazon.maps.engagement.widget.annotation.models.MediaAnnotation

internal enum class Action {
    ADD,
    MOVE,
    RESCALE,
}

internal data class Edit(
    val id: Int,
    val action: Action,
    val previousX: Float,
    val previousY: Float,
    val previousWidth: Int,
    val previousHeight: Int,
    val newX: Float,
    val newY: Float,
    val newWidth: Int,
    val newHeight: Int,
)

/** This class will be used to create a container for annotations to be added to. */
internal class AnnotationFrameLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : FrameLayout(context, attrs, defStyleAttr) {
    private var editHistory: ArrayDeque<Edit> = ArrayDeque()
    private var redoHistory: ArrayDeque<Edit> = ArrayDeque()

    init {
        isFocusable = true
        isFocusableInTouchMode = true
        setOnClickListener {
            requestFocus()
        }
    }

    /** Create a View that is a representation of an Annotation object.
     * @param mediaAnnotation The annotation object that will be represented.
     * @param onAnnotationEvent Callback for annotation events.
     * @return a View that represents an Annotation.
     */
    internal fun createAnnotation(
        mediaAnnotation: MediaAnnotation,
        onAnnotationEvent: ((AnnotationEvent) -> Unit)? = null,
    ): View {
        val annotationView = AnnotationView(
            context = context,
            annotationType = mediaAnnotation,
            onEvent = onAnnotationEvent,
        ).apply {
            id = mediaAnnotation.id
            mediaAnnotation.imageResource?.let { setImageResource(it) }
            x = mediaAnnotation.x
            y = mediaAnnotation.y
            layoutParams = LayoutParams(mediaAnnotation.width, mediaAnnotation.height)
            visibility = mediaAnnotation.visibility
            setColorFilter(ContextCompat.getColor(context, mediaAnnotation.colorID))
        }

        clearRedoHistory() // Clear redo history when new edit is made
        editHistory.add(
            Edit(
                mediaAnnotation.id,
                Action.ADD,
                previousX = 0f, // No previous state for ADD
                previousY = 0f,
                previousWidth = 0,
                previousHeight = 0,
                newX = mediaAnnotation.x,
                newY = mediaAnnotation.y,
                newWidth = mediaAnnotation.width,
                newHeight = mediaAnnotation.height,
            ),
        )

        return annotationView
    }

    /** Recreate an annotation view without affecting edit history.
     * This is used for redo operations to restore views without adding new edits.
     * @param mediaAnnotation The annotation object that will be represented.
     * @param onAnnotationEvent Callback for annotation events.
     * @return a View that represents an Annotation.
     */
    internal fun recreateAnnotationView(
        mediaAnnotation: MediaAnnotation,
        onAnnotationEvent: ((AnnotationEvent) -> Unit)? = null,
    ): View {
        val annotationView = AnnotationView(
            context = context,
            annotationType = mediaAnnotation,
            onEvent = onAnnotationEvent,
        ).apply {
            id = mediaAnnotation.id
            mediaAnnotation.imageResource?.let { setImageResource(it) }
            x = mediaAnnotation.x
            y = mediaAnnotation.y
            layoutParams = LayoutParams(mediaAnnotation.width, mediaAnnotation.height)
            visibility = mediaAnnotation.visibility
            setColorFilter(ContextCompat.getColor(context, mediaAnnotation.colorID))
        }

        // Don't modify edit history - this is for redo operations
        return annotationView
    }

    /** Create a bitmap from the FrameLayout.
     * @return a Bitmap of the FrameLayout.
     */
    internal fun getBitmapFromView(): Bitmap {
        requestFocus()
        val bitmap = createBitmap(width, height)
        val canvas = Canvas(bitmap)
        draw(canvas)
        return bitmap
    }

    /** Enables undo operations by retrieving the latest Edit from EditHistory.
     * @return an Edit object that was created most recently.
     */
    internal fun popLastEdit(): Edit? {
        val edit = editHistory.removeLastOrNull()
        edit?.let { redoHistory.add(it) }
        return edit
    }

    /** Enables redo operations by retrieving the latest Edit from RedoHistory.
     * @return an Edit object that was most recently undone.
     */
    internal fun popLastRedo(): Edit? {
        val edit = redoHistory.removeLastOrNull()
        edit?.let { editHistory.add(it) }
        return edit
    }

    /** Clear redo history when a new edit is made (standard undo/redo behavior).
     */
    private fun clearRedoHistory() {
        redoHistory.clear()
    }

    /** Add a new Edit into EditHistory when an annotation is repositioned.
     * @param mediaAnnotation: the annotation that was repositioned
     * @param previousX: the previous X position before repositioning
     * @param previousY: the previous Y position before repositioning
     */
    internal fun repositionAnnotation(
        mediaAnnotation: MediaAnnotation,
        previousX: Float,
        previousY: Float,
    ) {
        clearRedoHistory() // Clear redo history when new edit is made
        val recentEdit = Edit(
            mediaAnnotation.id,
            Action.MOVE,
            previousX = previousX,
            previousY = previousY,
            previousWidth = mediaAnnotation.width,
            previousHeight = mediaAnnotation.height,
            newX = mediaAnnotation.x,
            newY = mediaAnnotation.y,
            newWidth = mediaAnnotation.width,
            newHeight = mediaAnnotation.height,
        )
        editHistory.add(recentEdit)
    }

    internal fun rescaleAnnotation(
        mediaAnnotation: MediaAnnotation,
        previousX: Float,
        previousY: Float,
        previousWidth: Int,
        previousHeight: Int,
    ) {
        clearRedoHistory() // Clear redo history when new edit is made
        val recentEdit = Edit(
            mediaAnnotation.id,
            Action.RESCALE,
            previousX = previousX,
            previousY = previousY,
            previousWidth = previousWidth,
            previousHeight = previousHeight,
            newX = mediaAnnotation.x,
            newY = mediaAnnotation.y,
            newWidth = mediaAnnotation.width,
            newHeight = mediaAnnotation.height,
        )
        editHistory.add(recentEdit)
    }
}
