package com.amazon.maps.engagement.widget.annotation

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.Gravity
import android.view.MotionEvent
import android.view.View
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.view.updateLayoutParams
import com.amazon.maps.engagement.sdk.R
import com.amazon.maps.engagement.widget.annotation.models.MediaAnnotation
import kotlin.math.abs

/**
 * This class is used to create annotations that can be resized and rescaled. The annotations can
 * either be represented by an image or a text box.
 */
@SuppressLint("ClickableViewAccessibility")
internal class AnnotationView @JvmOverloads constructor(
    context: Context,
    val annotationType: MediaAnnotation = MediaAnnotation.Shapes(),
    val onEvent: ((AnnotationEvent) -> Unit)? = null,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0,
) : ConstraintLayout(context, attrs, defStyleAttr) {

    // Annotations
    private var textBox: EditText? = null
    private var imageView: ImageView? = null

    // View components for repositioning / resizing view
    private val border: View = View(context).apply {
        layoutParams = LayoutParams(
            LayoutParams.MATCH_CONSTRAINT,
            LayoutParams.MATCH_CONSTRAINT,
        ).apply {
            startToStart = LayoutParams.PARENT_ID
            topToTop = LayoutParams.PARENT_ID
            endToEnd = LayoutParams.PARENT_ID
            bottomToBottom = LayoutParams.PARENT_ID
            setMargins(HANDLE_SIZE / THREE, HANDLE_SIZE / THREE, HANDLE_SIZE / THREE, HANDLE_SIZE / THREE)
        }
        setBackgroundResource(R.drawable.annotation_resizable_border)
    }
    private val handles: MutableList<View> = mutableListOf()

    private enum class HandlePosition {
        TOP_LEFT,
        TOP_MIDDLE,
        TOP_RIGHT,
        MIDDLE_LEFT,
        MIDDLE_RIGHT,
        BOTTOM_LEFT,
        BOTTOM_MIDDLE,
        BOTTOM_RIGHT,
    }

    init {
        // Setup focus configurations for view
        isFocusable = true
        isFocusableInTouchMode = true
        this.setOnFocusChangeListener { _, hasFocus ->
            border.visibility = if (hasFocus) View.VISIBLE else View.GONE
            for (handle in handles) {
                handle.visibility = if (hasFocus) View.VISIBLE else View.GONE
            }
        }
        requestFocus()

        // Add either a ImageView or EditText view to parent view
        when (annotationType) {
            is MediaAnnotation.Shapes -> {
                imageView = ImageView(context).apply {
                    layoutParams = LayoutParams(
                        LayoutParams.MATCH_CONSTRAINT,
                        LayoutParams.MATCH_CONSTRAINT,
                    ).apply {
                        startToStart = LayoutParams.PARENT_ID
                        topToTop = LayoutParams.PARENT_ID
                        endToEnd = LayoutParams.PARENT_ID
                        bottomToBottom = LayoutParams.PARENT_ID
                    }
                }
                imageView?.setOnClickListener {
                    this.requestFocus()
                }
                addView(imageView)
            }

            is MediaAnnotation.Text -> {
                textBox = EditText(context).apply {
                    layoutParams = LayoutParams(
                        LayoutParams.MATCH_CONSTRAINT,
                        LayoutParams.MATCH_CONSTRAINT,
                    ).apply {
                        startToStart = LayoutParams.PARENT_ID
                        topToTop = LayoutParams.PARENT_ID
                        endToEnd = LayoutParams.PARENT_ID
                        bottomToBottom = LayoutParams.PARENT_ID
                        setMargins(HANDLE_SIZE / 2, HANDLE_SIZE / 2, HANDLE_SIZE / 2, HANDLE_SIZE / 2)
                    }
                    setBackgroundResource(R.drawable.annotation_item_text_box_themed)
                    gravity = Gravity.CENTER
                }
                textBox?.setOnTouchListener { _, _ ->
                    if (textBox?.hasFocus() == false) {
                        this.requestFocus()
                        true
                    } else {
                        false
                    }
                }
                addView(textBox)
            }
        }

        // Add view components that handle repositioning / resizing view
        border.setOnTouchListener(RepositionTouchListener())
        addView(border)

        // Add all 8 resize handles for classic resizing experience
        addView(createHandles(HandlePosition.TOP_LEFT))
        addView(createHandles(HandlePosition.TOP_MIDDLE))
        addView(createHandles(HandlePosition.TOP_RIGHT))
        addView(createHandles(HandlePosition.MIDDLE_LEFT))
        addView(createHandles(HandlePosition.MIDDLE_RIGHT))
        addView(createHandles(HandlePosition.BOTTOM_LEFT))
        addView(createHandles(HandlePosition.BOTTOM_MIDDLE))
        addView(createHandles(HandlePosition.BOTTOM_RIGHT))
    }

    // ImageView functions
    internal fun setImageResource(resId: Int) = imageView?.setImageResource(resId)
    internal fun setColorFilter(color: Int) = imageView?.setColorFilter(color)

    // EditText functions
    internal fun setInputType(type: Int) = textBox?.setInputType(type)
    internal fun setHint(text: String) = textBox?.setHint(text)
    internal fun setTextColor(color: Int) = textBox?.setTextColor(color)
    internal fun setHintTextColor(color: Int) = textBox?.setHintTextColor(color)
    internal fun setMinLines(lines: Int) = textBox?.setMinLines(lines)
    internal fun setMaxLines(lines: Int) = textBox?.setMaxLines(lines)
    internal fun setEditTextVerticalScrollBarEnabled(enabled: Boolean) = textBox?.setVerticalScrollBarEnabled(enabled)
    internal fun setEditTextBackgroundResource(resId: Int) = textBox?.setBackgroundResource(resId)
    internal fun setSingleLine(isSingleLine: Boolean) = textBox?.setSingleLine(isSingleLine)
    internal fun setTextStyle(style: Int) = textBox?.setTypeface(null, style)

    private fun createHandles(handlePosition: HandlePosition): View {
        val handleView = View(context).apply {
            layoutParams = LayoutParams(HANDLE_SIZE, HANDLE_SIZE).apply {
                // Position the handle based on its position type
                when (handlePosition) {
                    // Top row
                    HandlePosition.TOP_LEFT -> {
                        topToTop = LayoutParams.PARENT_ID
                        startToStart = LayoutParams.PARENT_ID
                    }

                    HandlePosition.TOP_MIDDLE -> {
                        topToTop = LayoutParams.PARENT_ID
                        startToStart = LayoutParams.PARENT_ID
                        endToEnd = LayoutParams.PARENT_ID
                    }

                    HandlePosition.TOP_RIGHT -> {
                        topToTop = LayoutParams.PARENT_ID
                        endToEnd = LayoutParams.PARENT_ID
                    }

                    // Middle row
                    HandlePosition.MIDDLE_LEFT -> {
                        topToTop = LayoutParams.PARENT_ID
                        bottomToBottom = LayoutParams.PARENT_ID
                        startToStart = LayoutParams.PARENT_ID
                    }

                    HandlePosition.MIDDLE_RIGHT -> {
                        topToTop = LayoutParams.PARENT_ID
                        bottomToBottom = LayoutParams.PARENT_ID
                        endToEnd = LayoutParams.PARENT_ID
                    }

                    // Bottom row
                    HandlePosition.BOTTOM_LEFT -> {
                        bottomToBottom = LayoutParams.PARENT_ID
                        startToStart = LayoutParams.PARENT_ID
                    }

                    HandlePosition.BOTTOM_MIDDLE -> {
                        bottomToBottom = LayoutParams.PARENT_ID
                        startToStart = LayoutParams.PARENT_ID
                        endToEnd = LayoutParams.PARENT_ID
                    }

                    HandlePosition.BOTTOM_RIGHT -> {
                        bottomToBottom = LayoutParams.PARENT_ID
                        endToEnd = LayoutParams.PARENT_ID
                    }
                }
            }
            setBackgroundResource(R.drawable.resize_handle_circle)
            setOnTouchListener(ResizeTouchListener(handlePosition))
        }
        handles.add(handleView)
        return handleView
    }

    private inner class RepositionTouchListener : OnTouchListener {
        private var initialX = 0f
        private var initialY = 0f
        private var focusDetector = GestureDetector(context, TapToFocusDetector())

        override fun onTouch(
            view: View,
            event: MotionEvent,
        ): Boolean {
            if (focusDetector.onTouchEvent(event)) return false
            when (event.actionMasked) {
                MotionEvent.ACTION_DOWN -> {
                    initialX = x - event.rawX
                    initialY = y - event.rawY
                }

                MotionEvent.ACTION_MOVE -> {
                    val dx = event.rawX - initialX
                    val dy = event.rawY - initialY
                    if (abs(dx) > DRAG_THRESHOLD || abs(dy) > DRAG_THRESHOLD) {
                        animate()
                            .x(event.rawX + initialX)
                            .y(event.rawY + initialY)
                            .setDuration(0)
                            .start()
                    }
                }

                MotionEvent.ACTION_UP -> {
                    (parent as? AnnotationFrameLayout)?.let {
                        onEvent?.invoke(
                            AnnotationEvent.Repositioned(
                                it,
                                id,
                                event.rawX + initialX,
                                event.rawY + initialY,
                            ),
                        )
                    }
                }
            }
            return true
        }

        private inner class TapToFocusDetector : GestureDetector.SimpleOnGestureListener() {
            override fun onDoubleTap(e: MotionEvent): Boolean {
                if (annotationType is MediaAnnotation.Text) {
                    textBox?.requestFocus()
                }
                return true
            }
        }
    }

    private inner class ResizeTouchListener(val handle: HandlePosition) : OnTouchListener {
        private var initialX = 0f
        private var initialY = 0f
        private var lastX = 0f
        private var lastY = 0f
        private var initialWidth = 0
        private var initialHeight = 0
        private var lastWidth = 0f
        private var lastHeight = 0f
        private var initialLeft = 0
        private var initialBottom = 0

        override fun onTouch(
            view: View,
            event: MotionEvent,
        ): Boolean {
            when (event.actionMasked) {
                MotionEvent.ACTION_DOWN -> handleActionDown(event)
                MotionEvent.ACTION_MOVE -> handleActionMove(event)
                MotionEvent.ACTION_UP -> handleActionUp(event)
            }
            return true
        }

        private fun handleActionDown(event: MotionEvent) {
            initialX = x - event.rawX
            initialY = y - event.rawY
            lastX = event.rawX
            lastY = event.rawY
            initialWidth = width
            initialHeight = height
            lastWidth = width.toFloat()
            lastHeight = height.toFloat()
            initialLeft = left
            initialBottom = bottom
        }

        private fun handleActionMove(event: MotionEvent) {
            val dx = event.rawX - lastX
            val dy = event.rawY - lastY
            lastX = event.rawX
            lastY = event.rawY

            if (handle in listOf(HandlePosition.TOP_LEFT, HandlePosition.MIDDLE_LEFT, HandlePosition.BOTTOM_LEFT)) {
                handleLeftResize(dx, event)
            }
            if (handle in listOf(HandlePosition.TOP_RIGHT, HandlePosition.MIDDLE_RIGHT, HandlePosition.BOTTOM_RIGHT)) {
                handleRightResize(dx)
            }
            if (handle in listOf(HandlePosition.TOP_LEFT, HandlePosition.TOP_MIDDLE, HandlePosition.TOP_RIGHT)) {
                handleTopResize(dy, event)
            }
            if (handle in listOf(HandlePosition.BOTTOM_LEFT, HandlePosition.BOTTOM_MIDDLE, HandlePosition.BOTTOM_RIGHT)) {
                handleBottomResize(dy)
            }
        }

        private fun handleActionUp(event: MotionEvent) {
            (parent as? AnnotationFrameLayout)?.let {
                onEvent?.invoke(
                    AnnotationEvent.Rescaled(
                        it,
                        id,
                        event.rawX + initialX,
                        event.rawY + initialY,
                        initialWidth,
                        initialHeight,
                    ),
                )
            }
        }

        private fun handleLeftResize(
            dx: Float,
            event: MotionEvent,
        ) {
            if (abs(dx) > DRAG_THRESHOLD) {
                updateLayoutParams<FrameLayout.LayoutParams> {
                    x = event.rawX
                    width = (lastWidth - dx).toInt()
                    left = initialLeft
                }
                lastWidth -= dx
            }
        }

        private fun handleRightResize(dx: Float) {
            if (abs(dx) > DRAG_THRESHOLD) {
                updateLayoutParams<FrameLayout.LayoutParams> {
                    width = (lastWidth + dx).toInt()
                }
                lastWidth += dx
            }
        }

        private fun handleTopResize(
            dy: Float,
            event: MotionEvent,
        ) {
            if (abs(dy) > DRAG_THRESHOLD) {
                val parentLocation = IntArray(2)
                (parent as View).getLocationOnScreen(parentLocation)
                val parentY = parentLocation[1]
                val relativeY = event.rawY - parentY

                updateLayoutParams<FrameLayout.LayoutParams> {
                    y = relativeY
                    height = (lastHeight - dy).toInt()
                    bottom = initialBottom
                }
                lastHeight -= dy
            }
        }

        private fun handleBottomResize(dy: Float) {
            if (abs(dy) > DRAG_THRESHOLD) {
                updateLayoutParams<FrameLayout.LayoutParams> {
                    height = (lastHeight + dy).toInt()
                }
                lastHeight += dy
            }
        }
    }

    companion object {
        const val HANDLE_SIZE = 40
        const val DRAG_THRESHOLD = 5
        const val THREE = 3
    }
}
