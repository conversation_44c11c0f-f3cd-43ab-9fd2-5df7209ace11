package com.amazon.maps.engagement.widget

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.core.graphics.createBitmap
import androidx.core.graphics.scale
import androidx.core.view.isVisible
import androidx.fragment.app.DialogFragment
import com.amazon.majixplatform.featureflag.FeatureFlag
import com.amazon.majixplatform.featureflag.FeatureTreatment
import com.amazon.majixplatform.log.logError
import com.amazon.majixplatform.util.MajixPlatformModule
import com.amazon.maps.engagement.sdk.R
import com.amazon.maps.engagement.widget.annotation.Action
import com.amazon.maps.engagement.widget.annotation.AnnotationEvent
import com.amazon.maps.engagement.widget.annotation.AnnotationFrameLayout
import com.amazon.maps.engagement.widget.annotation.models.MediaAnnotation
import com.amazon.meridian.text.MeridianText
import com.amazon.rds.buttonlayout.RDSButtonLayout
import java.io.ByteArrayOutputStream

class PreviewMediaDialog : DialogFragment() {

    private var title: String? = null
    private var mediaBytes: ByteArray? = null
    private var handleDeleteAction: (() -> Unit)? = null

    // Image annotation
    private var onSave: ((ByteArray) -> Unit)? = null
    private var onUpdatePreview: ((Bitmap) -> Unit)? = null
    private var originalBitmap: Bitmap? = null

    private lateinit var annotationContainer: AnnotationFrameLayout
    private lateinit var shapesSubmenu: LinearLayout
    private lateinit var shapesButton: ImageButton
    private val annotations = mutableListOf<MediaAnnotation>()
    private var currentSelectedButton: ImageButton? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.FullScreenDialog)
        title = arguments?.getString(EXTRA_TITLE)
        mediaBytes = arguments?.getByteArray(EXTRA_MEDIA_BYTES)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View? {
        val bytes = mediaBytes
        if (bytes == null) {
            logError { "No media to display. Dismissing dialog." }
            dismissAllowingStateLoss()
            return null
        }

        originalBitmap = try {
            BitmapFactory.decodeByteArray(bytes, 0, bytes.size)
        } catch (e: IllegalArgumentException) {
            logError { "Unable to decode bytes: ${e.message}. Dismissing dialog." }
            dismissAllowingStateLoss()
            return null
        }

        val bitmap = originalBitmap
        if (bitmap == null) {
            logError { "Failed to decode bitmap. Dismissing dialog." }
            dismissAllowingStateLoss()
            return null
        }

        val view = if (MajixPlatformModule.featureFlagging.isFeatureEnabled(FeatureFlag.ShowSVFAnnotationView) != FeatureTreatment.C) {
            inflater.inflate(R.layout.feedback_preview_media_annotation_dialog, container, false).apply {
                findViewById<MeridianText>(R.id.title).text = title
                findViewById<ImageView>(R.id.photo).setImageBitmap(bitmap)

                annotationContainer = findViewById(R.id.annotation_container)
                shapesSubmenu = findViewById(R.id.shapes_submenu)
                shapesButton = findViewById(R.id.shapes_btn)

                findViewById<View>(R.id.annotation_container)?.visibility = View.VISIBLE
                findViewById<View>(R.id.shapes_btn)?.visibility = View.VISIBLE
                findViewById<View>(R.id.undo_btn)?.visibility = View.VISIBLE

                findViewById<RDSButtonLayout>(R.id.previewLayout)?.apply {
                    setPrimaryButtonOnClickListener {
                        saveAnnotatedImage()
                    }
                    setSecondaryButtonOnClickListener {
                        handleDeleteAction?.invoke()
                        dismiss()
                    }
                }
            }
        } else {
            // Annotation feature disabled - use simple preview layout
            inflater.inflate(R.layout.feedback_preview_media_dialog, container, false).apply {
                findViewById<MeridianText>(R.id.title).text = title
                findViewById<ImageView>(R.id.photo).setImageBitmap(bitmap)
                findViewById<RDSButtonLayout>(R.id.previewLayout)?.apply {
                    setPrimaryButtonOnClickListener { dismiss() }
                    setSecondaryButtonOnClickListener {
                        handleDeleteAction?.invoke()
                        dismiss()
                    }
                }
            }
        }
        return view
    }

    override fun onViewCreated(
        view: View,
        savedInstanceState: Bundle?,
    ) {
        super.onViewCreated(view, savedInstanceState)
        if (MajixPlatformModule.featureFlagging.isFeatureEnabled(FeatureFlag.ShowSVFAnnotationView) != FeatureTreatment.C) {
            setupAnnotationButtons()
            setupMenuButtons()
        }
    }

    private fun setupMenuButtons() {
        shapesButton.setOnClickListener {
            val wasVisible = shapesSubmenu.isVisible
            shapesSubmenu.visibility = if (wasVisible) View.INVISIBLE else View.VISIBLE

            // Clear selection when hiding submenu
            if (wasVisible) {
                clearButtonSelection()
            }
        }

        view?.findViewById<ImageButton>(R.id.lines_btn)?.setOnClickListener {
            addAnnotation(MediaAnnotation.Shapes.Arrow())
        }

        view?.findViewById<ImageButton>(R.id.undo_btn)?.setOnClickListener {
            undoLastEdit()
        }

        view?.findViewById<ImageButton>(R.id.redo_btn)?.setOnClickListener {
            redoLastEdit()
        }
    }

    private fun setupAnnotationButtons() {
        view?.findViewById<ImageButton>(R.id.pin_btn)?.setOnClickListener { button ->
            selectButton(button as ImageButton)
            addAnnotation(MediaAnnotation.Shapes.Pin())
        }
        view?.findViewById<ImageButton>(R.id.rect_btn)?.setOnClickListener { button ->
            selectButton(button as ImageButton)
            addAnnotation(MediaAnnotation.Shapes.Rectangle())
        }
        view?.findViewById<ImageButton>(R.id.circle_btn)?.setOnClickListener { button ->
            selectButton(button as ImageButton)
            addAnnotation(MediaAnnotation.Shapes.Circle())
        }
        view?.findViewById<ImageButton>(R.id.cross_btn)?.setOnClickListener { button ->
            selectButton(button as ImageButton)
            addAnnotation(MediaAnnotation.Shapes.Cross())
        }
        view?.findViewById<ImageButton>(R.id.check_btn)?.setOnClickListener { button ->
            selectButton(button as ImageButton)
            addAnnotation(MediaAnnotation.Shapes.Check())
        }
        view?.findViewById<ImageButton>(R.id.text_btn)?.setOnClickListener { button ->
            selectButton(button as ImageButton)
            addAnnotation(MediaAnnotation.Text())
        }
    }

    private fun selectButton(button: ImageButton) {
        // Clear previous selection
        currentSelectedButton?.isSelected = false

        // Set new selection
        button.isSelected = true
        currentSelectedButton = button
    }

    private fun clearButtonSelection() {
        currentSelectedButton?.isSelected = false
        currentSelectedButton = null
    }

    private fun undoLastEdit() {
        val edit = annotationContainer.popLastEdit() ?: return

        when (edit.action) {
            Action.ADD -> {
                // Remove the view from container but keep annotation data for redo
                annotationContainer.findViewById<View>(edit.id)?.let { view ->
                    annotationContainer.removeView(view)
                }
                // Don't remove from annotations list - keep it for redo functionality
            }

            Action.MOVE -> {
                // Restore previous position
                annotations.find { it.id == edit.id }?.let { annotation ->
                    annotation.x = edit.previousX
                    annotation.y = edit.previousY
                    annotationContainer.findViewById<View>(edit.id)?.let { view ->
                        view.x = edit.previousX
                        view.y = edit.previousY
                    }
                }
            }

            Action.RESCALE -> {
                // Restore previous size and position
                annotations.find { it.id == edit.id }?.let { annotation ->
                    annotation.x = edit.previousX
                    annotation.y = edit.previousY
                    annotation.width = edit.previousWidth
                    annotation.height = edit.previousHeight
                    annotationContainer.findViewById<View>(edit.id)?.let { view ->
                        view.x = edit.previousX
                        view.y = edit.previousY
                        view.layoutParams = view.layoutParams.apply {
                            width = edit.previousWidth
                            height = edit.previousHeight
                        }
                    }
                }
            }
        }
    }

    private fun redoLastEdit() {
        val edit = annotationContainer.popLastRedo() ?: return

        when (edit.action) {
            Action.ADD -> {
                annotations.find { it.id == edit.id }?.let { annotation ->
                    val annotationView = annotationContainer.recreateAnnotationView(annotation) { event ->
                        handleAnnotationEvent(event)
                    }
                    annotationContainer.addView(annotationView)
                }
            }

            Action.MOVE -> {
                // Restore to the "new" position (the position after the move)
                annotations.find { it.id == edit.id }?.let { annotation ->
                    annotation.x = edit.newX
                    annotation.y = edit.newY
                    annotationContainer.findViewById<View>(edit.id)?.let { view ->
                        view.x = edit.newX
                        view.y = edit.newY
                    }
                }
            }

            Action.RESCALE -> {
                // Restore to the "new" size and position (after the rescale)
                annotations.find { it.id == edit.id }?.let { annotation ->
                    annotation.x = edit.newX
                    annotation.y = edit.newY
                    annotation.width = edit.newWidth
                    annotation.height = edit.newHeight
                    annotationContainer.findViewById<View>(edit.id)?.let { view ->
                        view.x = edit.newX
                        view.y = edit.newY
                        view.layoutParams = view.layoutParams.apply {
                            width = edit.newWidth
                            height = edit.newHeight
                        }
                    }
                }
            }
        }
    }

    private fun handleAnnotationEvent(event: AnnotationEvent) {
        when (event) {
            is AnnotationEvent.Drawn -> {
                // Annotation has been drawn
            }

            is AnnotationEvent.Repositioned -> {
                annotations.find { it.id == event.id }?.let { annotation ->
                    val previousX = annotation.x
                    val previousY = annotation.y
                    annotation.x = event.x
                    annotation.y = event.y
                    event.container.repositionAnnotation(annotation, previousX, previousY)
                }
            }

            is AnnotationEvent.Rescaled -> {
                annotations.find { it.id == event.id }?.let { annotation ->
                    val previousX = annotation.x
                    val previousY = annotation.y
                    val previousWidth = annotation.width
                    val previousHeight = annotation.height
                    annotation.x = event.x
                    annotation.y = event.y
                    annotation.width = event.width
                    annotation.height = event.height
                    event.container.rescaleAnnotation(annotation, previousX, previousY, previousWidth, previousHeight)
                }
            }

            is AnnotationEvent.Deleted -> {
                annotations.removeIf { it.id == event.id }
            }
        }
    }

    private fun saveAnnotatedImage() {
        try {
            val annotatedBitmap = annotationContainer.getBitmapFromView()
            val combinedBitmap = combineOriginalAndAnnotations(annotatedBitmap)

            // Update preview first with the combined bitmap
            // MediaWidget will handle bitmap lifecycle
            onUpdatePreview?.invoke(combinedBitmap)

            // Save bytes for response
            val stream = ByteArrayOutputStream()
            combinedBitmap.compress(Bitmap.CompressFormat.JPEG, MAX_QUALITY, stream)
            val bytes = stream.toByteArray()
            stream.close()
            onSave?.invoke(bytes)

            // Clean up temporary bitmap only
            annotatedBitmap.recycle()

            dismiss()
        } catch (e: Exception) {
            logError { "Error saving annotated image: ${e.message}" }
            dismiss()
        }
    }

    private fun addAnnotation(annotation: MediaAnnotation) {
        annotations.add(annotation)
        val annotationView = annotationContainer.createAnnotation(annotation) { event ->
            handleAnnotationEvent(event)
        }
        annotationContainer.addView(annotationView)
        shapesSubmenu.visibility = View.INVISIBLE
        clearButtonSelection()
    }

    fun setOnSaveListener(listener: (ByteArray) -> Unit) {
        onSave = listener
    }

    fun setOnUpdatePreviewListener(listener: (Bitmap) -> Unit) {
        onUpdatePreview = listener
    }

    private fun combineOriginalAndAnnotations(annotatedBitmap: Bitmap): Bitmap {
        val original = originalBitmap ?: return annotatedBitmap.copy(annotatedBitmap.config, true)

        // Create a new bitmap with the same dimensions as the original
        val combined = createBitmap(original.width, original.height)
        val canvas = Canvas(combined)

        try {
            // Draw the original image first
            canvas.drawBitmap(original, 0f, 0f, null)

            // Scale the annotated bitmap to match original dimensions if needed
            if (annotatedBitmap.width != original.width || annotatedBitmap.height != original.height) {
                val scaledAnnotated = annotatedBitmap.scale(original.width, original.height)
                canvas.drawBitmap(scaledAnnotated, 0f, 0f, null)
                scaledAnnotated.recycle()
            } else {
                canvas.drawBitmap(annotatedBitmap, 0f, 0f, null)
            }

            return combined
        } catch (e: Exception) {
            logError { "Error combining bitmaps: ${e.message}" }
            return annotatedBitmap.copy(annotatedBitmap.config, true)
        }
    }

    companion object {
        const val EXTRA_MEDIA_BYTES = "extra_media_bytes"
        const val EXTRA_TITLE = "extra_title"
        const val MAX_QUALITY = 100

        @JvmField
        val TAG: String = PreviewMediaDialog::class.java.simpleName

        @JvmStatic
        fun newInstance(
            title: String,
            bytes: ByteArray,
            handleDeleteAction: () -> Unit,
            onUpdatePreview: ((Bitmap) -> Unit)? = null,
        ) = PreviewMediaDialog().apply {
            arguments = Bundle().apply {
                putString(EXTRA_TITLE, title)
                putByteArray(EXTRA_MEDIA_BYTES, bytes)
            }
            this.handleDeleteAction = handleDeleteAction
            this.onUpdatePreview = onUpdatePreview
        }
    }
}
