/*
 * Copyright (c) 2023 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 */

package com.amazon.maps.engagement.widget

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.net.Uri
import android.util.Log
import android.view.LayoutInflater
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.net.toUri
import com.amazon.majixplatform.featureflag.FeatureFlag
import com.amazon.majixplatform.featureflag.FeatureTreatment
import com.amazon.majixplatform.util.MajixPlatformModule
import com.amazon.maps.engagement.model.FeedbackResponse
import com.amazon.maps.engagement.model.MediaLauncher
import com.amazon.maps.engagement.model.MediaMetadata
import com.amazon.maps.engagement.model.Question
import com.amazon.maps.engagement.model.contextualfeedback.QuestionIdConstants
import com.amazon.maps.engagement.sdk.R
import com.amazon.maps.engagement.utils.MediaUtils
import com.amazon.maps.engagement.viewmodel.ContextualFeedbackViewModel
import com.amazon.meridian.button.MeridianButton
import com.amazon.meridian.text.MeridianText
import com.google.gson.Gson
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.ByteArrayOutputStream

class MediaWidget(
    context: Context,
    question: Question,
    onResponseChanged: (String, FeedbackResponse?) -> Unit,
    private val mediaLauncher: MediaLauncher,
    private val coroutineScope: CoroutineScope,
    private val feedbackViewModel: ContextualFeedbackViewModel? = null,
) : BaseWidget(context, question, onResponseChanged) {

    private var mediaTitle: MeridianText
    private var mediaSubtitle: MeridianText
    private var addImageButton: MeridianButton
    private var takeImageButton: MeridianButton
    private var previewContainer: ConstraintLayout
    private var previewThumbnail: ImageView
    private var previewTitle: MeridianText
    private var deleteButton: FrameLayout
    private var selectedBitmap: Bitmap? = null
    private var currentUri: Uri? = null
    private var currentMetadata: MediaMetadata? = null
    private var editButtonContainer: LinearLayout? = null

    init {
        // Use different layouts based on feature flag
        if (MajixPlatformModule.featureFlagging.isFeatureEnabled(FeatureFlag.ShowSVFAnnotationView) != FeatureTreatment.C) {
            // Annotation feature enabled - use layout with edit button
            LayoutInflater.from(context).inflate(R.layout.media_view_layout_with_edit, this)
            // Initialize edit button container only when using the layout with edit button
            editButtonContainer = findViewById(R.id.edit_button_container)
        } else {
            // Annotation feature disabled - use standard layout
            LayoutInflater.from(context).inflate(R.layout.media_view_layout, this)
        }

        mediaTitle = findViewById(R.id.media_title)
        mediaSubtitle = findViewById(R.id.media_subtitle)
        addImageButton = findViewById(R.id.add_photo_button)
        takeImageButton = findViewById(R.id.take_photo_button)
        previewContainer = findViewById(R.id.preview_container)
        previewThumbnail = findViewById(R.id.preview_media_thumbnail)
        previewTitle = findViewById(R.id.preview_media_title)
        deleteButton = findViewById(R.id.delete_button)

        setupButtons()
        setupQuestionText()
    }

    private fun setupButtons() {
        takeImageButton.setOnClickListener {
            mediaLauncher.onLaunchCamera { uri, metadata ->
                uri?.let { handleMediaResult(it, metadata) }
            }
        }

        addImageButton.setOnClickListener {
            mediaLauncher.onLaunchGallery { uri, metadata ->
                uri?.let { handleMediaResult(it, metadata) }
            }
        }

        deleteButton.setOnClickListener {
            deleteMediaAndUpdateState()
        }

        previewThumbnail.setOnClickListener {
            selectedBitmap?.let { showFullScreenPreview(it) }
        }

        // Set up edit button click listener if the container exists
        editButtonContainer?.setOnClickListener {
            selectedBitmap?.let { showFullScreenPreview(it) }
        }
    }

    private fun handleMediaResult(
        uri: Uri,
        metadata: MediaMetadata? = null,
    ) {
        currentUri = uri
        currentMetadata = metadata
        coroutineScope.launch {
            try {
                val bitmap = withContext(Dispatchers.IO) {
                    MediaUtils.decodeBitmap(context, uri, feedbackViewModel?.getImageEncryptionManager())
                }
                bitmap?.let {
                    val mutableBitmap = it.copy(Bitmap.Config.ARGB_8888, true)
                    try {
                        val bytes = withContext(Dispatchers.Default) {
                            MediaUtils.encodeBitmapWithSizeLimit(
                                mutableBitmap,
                                Bitmap.CompressFormat.JPEG,
                                IMAGE_SIZE_LIMIT,
                            )
                        }
                        val processedBitmap = BitmapFactory.decodeByteArray(bytes, 0, bytes.size)
                        withContext(Dispatchers.Main) {
                            showPreview(processedBitmap)
                            notifyResponseChanged(createFeedbackResponse())
                        }
                    } finally {
                        mutableBitmap.recycle()
                        bitmap.recycle()
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error loading bitmap", e)
                withContext(Dispatchers.Main) {
                    clearPreview()
                }
            }
        }
    }

    private fun updateVisibility(showPreview: Boolean) {
        // Attach state views
        listOf(mediaTitle, addImageButton, takeImageButton).forEach {
            it.visibility = if (showPreview) GONE else VISIBLE
        }
        mediaSubtitle.visibility = if (showPreview || question.subtext.isNullOrEmpty()) GONE else VISIBLE

        // Preview state views
        listOf(previewContainer, previewTitle, previewThumbnail, deleteButton).forEach {
            it.visibility = if (showPreview) VISIBLE else GONE
        }

        // Only show edit button if it exists and we're showing a preview
        // This is only available when the annotation feature is enabled
        if (showPreview && MajixPlatformModule.featureFlagging.isFeatureEnabled(FeatureFlag.ShowSVFAnnotationView) != FeatureTreatment.C) {
            editButtonContainer?.visibility = VISIBLE
        } else {
            editButtonContainer?.visibility = GONE
        }
    }

    private fun showPreview(
        bitmap: Bitmap,
        saveToCache: Boolean = false,
    ) {
        // Clean up old bitmap before setting new one
        selectedBitmap?.recycle()
        selectedBitmap = bitmap

        if (saveToCache) {
            coroutineScope.launch {
                try {
                    // Convert bitmap to input stream
                    val byteArrayOutputStream = ByteArrayOutputStream()
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 100, byteArrayOutputStream)
                    val inputStream = byteArrayOutputStream.toByteArray().inputStream()

                    // Save to cache
                    val key = "annotated_${System.currentTimeMillis()}"
                    val filePath = feedbackViewModel?.getAssetCache()?.save(key, inputStream)

                    // Update current URI to point to cached file and notify response changed
                    filePath?.let {
                        currentUri = "file://$it".toUri()
                        withContext(Dispatchers.Main) {
                            val response = createFeedbackResponse()
                            notifyResponseChanged(response)
                        }
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Error saving annotated image to cache", e)
                }
            }
        }

        updateVisibility(true)
        previewTitle.text = context.getString(R.string.feedback_photo_title)
        previewThumbnail.setImageBitmap(bitmap)
    }

    private fun clearPreview() {
        cleanup()
        updateVisibility(false)
    }

    private fun showFullScreenPreview(bitmap: Bitmap) {
        coroutineScope.launch {
            val mutableBitmap = bitmap.copy(Bitmap.Config.ARGB_8888, true)
            val byteArray = withContext(Dispatchers.Default) {
                try {
                    MediaUtils.encodeBitmapWithSizeLimit(
                        mutableBitmap,
                        Bitmap.CompressFormat.JPEG,
                        IMAGE_SIZE_LIMIT,
                    )
                } finally {
                    mutableBitmap.recycle()
                }
            }

            withContext(Dispatchers.Main) {
                (context as? AppCompatActivity)?.supportFragmentManager?.let {
                    val dialog = PreviewMediaDialog.newInstance(
                        context.getString(R.string.feedback_photo_title),
                        byteArray,
                        ::deleteMediaAndUpdateState,
                    ) { annotatedBitmap ->
                        showPreview(annotatedBitmap, true)
                    }
                    dialog.show(it, PreviewMediaDialog.TAG)
                }
            }
        }
    }

    private fun setupQuestionText() {
        if (!question.text.isNullOrEmpty()) {
            mediaTitle.text = question.text
        } else {
            mediaTitle.visibility = GONE
        }

        if (!question.subtext.isNullOrEmpty()) {
            mediaSubtitle.text = question.subtext
        } else {
            mediaSubtitle.visibility = GONE
        }
    }

    override fun getResponse(): FeedbackResponse? {
        return currentUri?.let { createFeedbackResponse() }
    }

    private fun createFeedbackResponse(): FeedbackResponse {
        return FeedbackResponse(
            questionId = question.identifier,
            answerIdList = listOf(currentUri?.toString() ?: ""),
            metadata = currentMetadata?.let { Gson().toJson(it) },
        )
    }

    override fun setResponse(response: FeedbackResponse?) {
        if (response == null) {
            clearPreview()
            return
        }

        response.metadata?.let { metadataJson ->
            try {
                currentMetadata = Gson().fromJson(metadataJson, MediaMetadata::class.java)

                if (response.answerIdList.isNotEmpty()) {
                    val uri = Uri.parse(response.answerIdList[0])
                    currentUri = uri
                    coroutineScope.launch {
                        try {
                            val assetCache = feedbackViewModel?.getAssetCache()
                            val bitmap = if (uri.scheme == "file" && assetCache?.isUriFromCache(uri) == true) {
                                // Load from asset cache if it's a cached file
                                getKeyFromUri(uri)?.let { key ->
                                    assetCache.get(key)?.let { path ->
                                        withContext(Dispatchers.IO) {
                                            MediaUtils.decodeEncryptedBitmap(path, feedbackViewModel.getImageEncryptionManager())
                                        }
                                    }
                                }
                            } else {
                                // Load from original URI if not cached
                                withContext(Dispatchers.IO) {
                                    MediaUtils.decodeBitmap(context, uri, feedbackViewModel?.getImageEncryptionManager())
                                }
                            }

                            bitmap?.let {
                                withContext(Dispatchers.Main) {
                                    showPreview(it)
                                }
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "Error loading bitmap from response", e)
                            withContext(Dispatchers.Main) {
                                clearPreview()
                            }
                        }
                    }
                }
            } catch (e: Exception) {
                Log.e(TAG, "Error parsing metadata", e)
            }
        }
    }

    private fun deleteMediaAndUpdateState() {
        clearPreview()
        notifyResponseChanged(
            FeedbackResponse(
                questionId = QuestionIdConstants.ATTACH_MEDIA_QUESTION_ID,
                answerIdList = emptyList(),
                metadata = null,
            ),
        )
    }

    private fun cleanup() {
        selectedBitmap?.recycle()
        selectedBitmap = null

        // Clean up cached file if it exists
        currentUri?.let { uri ->
            getKeyFromUri(uri)?.let { key ->
                feedbackViewModel?.getAssetCache()?.remove(key)
            }
        }

        currentUri = null
        currentMetadata = null
        previewThumbnail.setImageBitmap(null)
    }

    private fun getKeyFromUri(uri: Uri): String? {
        return uri.path?.substringAfterLast("/")?.substringBefore(".")
    }

    companion object {
        private const val TAG = "MediaWidget"
        const val IMAGE_MIME_TYPE = "image/*"
        const val IMAGE_SIZE_LIMIT = 250000 // 250KB
    }
}
