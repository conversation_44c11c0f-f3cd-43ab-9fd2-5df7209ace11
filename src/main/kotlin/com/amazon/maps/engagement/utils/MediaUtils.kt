package com.amazon.maps.engagement.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.ImageDecoder
import android.graphics.Matrix
import android.media.ExifInterface
import android.net.Uri
import com.amazon.maps.engagement.security.ImageEncryptionManager
import com.amazon.geo.mapsv2.location.LocationEngineUtil
import com.amazon.location.model.geofence.LatLng
import com.amazon.majixplatform.log.logDebug
import com.amazon.majixplatform.log.logError
import com.amazon.maps.engagement.model.FeedbackResponse
import com.amazon.maps.engagement.model.MediaMetadata
import com.amazon.maps.engagement.model.MediaType
import com.google.gson.Gson
import java.io.ByteArrayOutputStream
import java.text.SimpleDateFormat
import java.util.Locale

internal object MediaUtils {
    private const val DOWNSAMPLE_SCALE = 0.5f
    private const val MAX_COMPRESSION_QUALITY = 100
    private const val MIN_COMPRESSION_QUALITY = 0
    private const val COMPRESSION_INTERVAL = 10
    private const val DEFAULT_COORDINATE = 2.0
    private const val MILLISECONDS_TO_SECONDS = 1000L
    private const val DATETIME_FORMAT = "yyyy:MM:dd HH:mm:ss"
    private const val DEFAULT_PROVIDER = "TrueSight"

    internal fun decodeBitmap(context: Context, uri: Uri, imageEncryptionManager: ImageEncryptionManager? = null): Bitmap? {
        return try {
            if (isEncryptedFile(uri)) {
                uri.path?.let { decodeEncryptedBitmap(it, imageEncryptionManager) }
            } else {
                ImageDecoder.decodeBitmap(
                    ImageDecoder.createSource(context.contentResolver, uri)
                )
            }
        } catch (e: Exception) {
            logError(throwable = e) { "Error decoding bitmap" }
            null
        }
    }

    /**
     * Decodes an encrypted image file into a Bitmap
     *
     * @param filePath Path to the encrypted image file
     * @param imageEncryptionManager The ImageEncryptionManager to use for decryption
     * @return Decoded Bitmap or null if decryption fails
     */
    internal fun decodeEncryptedBitmap(filePath: String, imageEncryptionManager: ImageEncryptionManager? = null): Bitmap? {
        if (imageEncryptionManager == null) {
            logError { "ImageEncryptionManager is null, cannot decrypt image" }
            return null
        }

        return try {
            imageEncryptionManager.decryptImage(filePath)
        } catch (e: Exception) {
            logError(e) { "Error decoding encrypted bitmap: $e" }
            null
        }
    }

    private fun isEncryptedFile(uri: Uri): Boolean {
        return uri.path?.endsWith(".${ImageEncryptionManager.FILE_EXTENSION}") == true
    }

    internal fun processImageForUpload(context: Context, uri: Uri, imageEncryptionManager: ImageEncryptionManager): ByteArray? {
        return try {
            decodeBitmap(context, uri, imageEncryptionManager)?.let { bitmap ->
                ByteArrayOutputStream().apply {
                    bitmap.compress(Bitmap.CompressFormat.JPEG, 100, this)
                }.toByteArray()
            }
        } catch (e: Exception) {
            logError(throwable = e) { "Error processing image for upload" }
            null
        }
    }

    internal fun getMediaMetadata(mediaResponse: FeedbackResponse?): List<MediaMetadata?> {
        return try {
            mediaResponse?.metadata?.let { json ->
                Gson().fromJson(json, MediaMetadata::class.java)?.let { metadata ->
                    listOf(metadata)
                }
            } ?: emptyList()
        } catch (e: Exception) {
            logError(throwable = e) { "Error parsing media metadata" }
            emptyList()
        }
    }

    /**
     * Extracts a [MediaMetadata] object from a file at a given Uri. This implementation relies on Exif data to be present.
     *
     * @param context Android context
     * @param mediaId id of media corresponding to metadata
     * @param uri file location
     * @return
     */
    internal fun extractMediaMetadataUsingExif(
        context: Context,
        mediaId: String,
        uri: Uri,
        mediaReferenceId: String,
    ): MediaMetadata? {
        val exif = context.contentResolver.openInputStream(uri)?.use {
            ExifInterface(it)
        } ?: return null

        // extract timestamp
        val format = SimpleDateFormat(DATETIME_FORMAT, Locale.US)
        val datetime = exif.getAttribute(ExifInterface.TAG_DATETIME)
        val timestamp = datetime?.let { format.parse(it)?.time } ?: System.currentTimeMillis()

        // extract location
        var extractExifLocationErrored = false
        val latLng = FloatArray(2)
        val location = if (exif.getLatLong(latLng)) {
            LatLng(latLng[0].toDouble(), latLng[1].toDouble())
        } else {
            extractExifLocationErrored = true
            LocationEngineUtil.getLocationPerLocationEngine()?.let {
                LatLng(it.latitude, it.longitude)
            } ?: LatLng(DEFAULT_COORDINATE, DEFAULT_COORDINATE)
            // Default to 2/2 for easier debugging purposes
            // Lots of places default it to 0/0 so it makes it hard for us to see our own errors
        }

        return createMediaMetadata(
            mediaId = mediaId,
            uri = uri,
            context = context,
            location = location,
            timestamp = timestamp,
            extractExifLocationErrored = extractExifLocationErrored,
            mediaReferenceId = mediaReferenceId,
        )
    }

    /**
     * Creates a [MediaMetadata] object for a camera-captured media.
     * This implementation uses current timestamp and device location.
     *
     * @param context Android context
     * @param mediaId id of media corresponding to metadata
     * @param uri file location
     * @param mediaReferenceId unique reference id for the media
     * @return
     */
    internal fun createMediaMetadataForCamera(
        context: Context,
        mediaId: String,
        uri: Uri,
        mediaReferenceId: String,
    ): MediaMetadata {
        val userLocation = LocationEngineUtil.getLocationPerLocationEngine()
        val location = userLocation?.let {
            LatLng(it.latitude, it.longitude)
        } ?: LatLng(DEFAULT_COORDINATE, DEFAULT_COORDINATE)

        return createMediaMetadata(
            mediaId = mediaId,
            uri = uri,
            context = context,
            location = location,
            timestamp = System.currentTimeMillis(),
            extractExifLocationErrored = false,
            mediaReferenceId = mediaReferenceId,
        )
    }

    /**
     * Encodes a bitmap to a [ByteArray], compressing and down-sampling until provided size limit is reached
     *
     * @param originalBitmap bitmap to be encoded to [ByteArray]
     * @param format compression format
     * @param sizeLimit size limit in bytes
     * @return ByteArray representation of compressed bitmap
     */
    internal fun encodeBitmapWithSizeLimit(
        originalBitmap: Bitmap,
        format: Bitmap.CompressFormat,
        sizeLimit: Int,
    ): ByteArray {
        val stream = ByteArrayOutputStream()
        var bitmap = originalBitmap.copy(originalBitmap.config, true)
        var quality = MAX_COMPRESSION_QUALITY

        do {
            if (quality < MIN_COMPRESSION_QUALITY) {
                logDebug { "Down-sampling bitmap to reach size limit" }
                val oldBitmap = bitmap
                val transformation = Matrix().apply {
                    postScale(DOWNSAMPLE_SCALE, DOWNSAMPLE_SCALE)
                }
                bitmap = Bitmap.createBitmap(
                    oldBitmap,
                    0,
                    0,
                    oldBitmap.width,
                    oldBitmap.height,
                    transformation,
                    false,
                )
                if (oldBitmap != originalBitmap) {
                    oldBitmap.recycle()
                }
                quality = MAX_COMPRESSION_QUALITY
            }

            stream.reset()
            bitmap.compress(format, quality, stream)
            quality -= COMPRESSION_INTERVAL
        } while (stream.size() > sizeLimit)

        return stream.toByteArray()
    }

    /**
     * Creates a [MediaMetadata] object using provided metadata information.
     *
     * @param mediaId id of media corresponding to metadata
     * @param uri file location
     * @param context Android context
     * @param location LatLng object representing the media location
     * @param timestamp time when the media was captured (in milliseconds since epoch)
     * @param extractExifLocationErrored indicating if there was an error extracting location from Exif
     * @param mediaReferenceId unique reference id for the media
     * @return [MediaMetadata] object
     */
    private fun createMediaMetadata(
        mediaId: String,
        uri: Uri,
        context: Context,
        location: LatLng,
        timestamp: Long,
        extractExifLocationErrored: Boolean,
        mediaReferenceId: String,
    ): MediaMetadata {
        val mimeType = context.contentResolver.getType(uri)
        val mediaType = mimeType?.let {
            MediaType.values().find { mimeType.startsWith(it.mime) }
        } ?: MediaType.UNKNOWN

        return MediaMetadata(
            mediaId = mediaId,
            mediaType = mediaType,
            latitude = location.latitude,
            longitude = location.longitude,
            timeSinceEpochInSeconds = timestamp / MILLISECONDS_TO_SECONDS,
            provider = DEFAULT_PROVIDER,
            extractExifLocationErrored = extractExifLocationErrored,
            mediaReferenceId = mediaReferenceId,
        )
    }
}
