package com.amazon.maps.engagement.utils

import android.content.Context
import android.location.Location
import com.amazon.geo.mapsv2.internal.mapbox.AGMetricsConfig
import com.amazon.geo.mapsv2.location.LocationEngineUtil
import com.amazon.geo.mapsv2.util.LOG_TAG
import com.amazon.geo.routing.GranTorinoInitializer
import com.amazon.geo.routingv2.elcamino.RouteStore
import com.amazon.location.model.geofence.LatLng
import com.amazon.majixplatform.log.MLog
import com.amazon.maps.engagement.model.FeedbackCategories
import com.amazon.maps.engagement.model.MapsFeedbackType
import com.amazon.maps.engagement.model.MediaMetadata
import com.amazon.maps.engagement.model.contextualfeedback.ContextualFeedbackMetadata
import com.amazon.maps.engagement.model.contextualfeedback.ContextualFeedbackResponseMetadata
import com.amazon.maps.engagement.model.contextualfeedback.ContextualFeedbackStopInfo

internal object ContextualFeedbackResponseMetadataBuilder {
    fun generateMetadata(
        context: Context,
        feedbackType: String,
        location: LatLng? = null,
        configProvider: AGMetricsConfig = AGMetricsConfig,
        routeStore: RouteStore = GranTorinoInitializer.routeStore,
        mapCaptureUUID: String? = null,
        userLocation: Location? = LocationEngineUtil.getLocationPerLocationEngine(),
        stop: ContextualFeedbackStopInfo? = null,
        mapsFeedbackMetadata: ContextualFeedbackMetadata?,
        mediaMetadata: List<MediaMetadata?> = listOf(),
        feedbackCategoriesVersion: Double? = null,
    ): ContextualFeedbackResponseMetadata {
        val route = stop?.addressId?.let { routeStore.getRouteForAddressId(it) }
        val countryCode = configProvider.countryCode()
        val operatingArea = configProvider.operatingArea()
        val obfuscatedTransporterId = configProvider.transporterId()
        val userAgent = context.packageName ?: DEFAULT_FEEDBACK_STRING
        val userPosition = userLocation?.let { LatLng(it.latitude, it.longitude) } ?: DEFAULT_LAT_LNG
        val userType = configProvider.transporterType()
        val routeId = route?.routeId ?: DEFAULT_FEEDBACK_STRING
        val subRouteId = route?.subRouteId ?: stop?.stopId ?: DEFAULT_FEEDBACK_STRING
        var feedbackPosition = location ?: DEFAULT_LAT_LNG
        val address = route?.address ?: DEFAULT_FEEDBACK_STRING
        val addressId = stop?.addressId ?: DEFAULT_FEEDBACK_STRING
        val requestId = route?.requestId ?: DEFAULT_FEEDBACK_STRING
        val mapCaptureMessageId = mapCaptureUUID?.let { "u/$obfuscatedTransporterId/out/$MAP_CAPTURE_MESSAGE_BROKER_TOPIC/$it" } ?: DEFAULT_FEEDBACK_STRING
        val stopId = stop?.stopId ?: DEFAULT_FEEDBACK_STRING
        val scannableId = stop?.scannableId ?: DEFAULT_FEEDBACK_STRING
        val stylesheetVersion = mapsFeedbackMetadata?.stylesheetVersion ?: DEFAULT_FEEDBACK_STRING
        val stylesheetUrl = mapsFeedbackMetadata?.stylesheetUrl ?: DEFAULT_FEEDBACK_STRING
        val roadEntryGeocode = stop?.roadEntryGeocode ?: DEFAULT_LAT_LNG
        val mediaInfo = mediaMetadata.mapNotNull { item ->
            item?.also { it.provider = "TrueSight" }
        }
        val deliveryPointGeocode = stop?.deliveryPointGeocode ?: DEFAULT_LAT_LNG

        val feedbackType = try {
            MapsFeedbackType.valueOf(feedbackType)
        } catch (_: Exception) {
            MLog.e(LOG_TAG, "Unknown feedback type of $feedbackType")
            MapsFeedbackType.UNKNOWN
        }

        if (feedbackType == MapsFeedbackType.MAPS_LOADING_SLOW) {
            feedbackPosition = userPosition
        }

        val resolvedFeedbackCategoriesVersion = feedbackCategoriesVersion
            ?: mapsFeedbackMetadata?.feedbackCategoriesVersion
            ?: FeedbackCategories.ENGAGEMENT_SDK_UI.version

        return ContextualFeedbackResponseMetadata(
            countryCode = countryCode,
            operatingArea = operatingArea,
            obfuscatedTransporterId = obfuscatedTransporterId,
            userAgent = userAgent,
            userPosition = userPosition,
            userType = userType,
            routeId = routeId,
            subRouteId = subRouteId,
            feedbackType = feedbackType,
            feedbackPosition = feedbackPosition,
            address = address,
            addressId = addressId,
            requestId = requestId,
            mapCaptureMessageId = mapCaptureMessageId,
            stopId = stopId,
            scannableId = scannableId,
            stylesheetVersion = stylesheetVersion,
            stylesheetUrl = stylesheetUrl,
            roadEntryGeocode = roadEntryGeocode,
            mediaInfo = mediaInfo,
            deliveryPointGeocode = deliveryPointGeocode,
            feedbackCategoriesVersion = resolvedFeedbackCategoriesVersion,
        )
    }

    private const val MAP_CAPTURE_MESSAGE_BROKER_TOPIC = "MAJIXFeedbackMapCapture"
    private val DEFAULT_LAT_LNG = LatLng(0.0, 0.0)
    private val DEFAULT_FEEDBACK_TYPE = MapsFeedbackType.UNKNOWN
    private const val DEFAULT_FEEDBACK_STRING = "Attribute is not populated"
}
